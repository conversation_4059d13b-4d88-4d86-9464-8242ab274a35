name: Deploy Infrastructure

on:
  push:
    branches: [main]
    paths: ['infrastructure/**']
  pull_request:
    branches: [main]
    paths: ['infrastructure/**']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production
      domain_name:
        description: 'Domain name'
        required: true
        default: 'hi-vi.com'
        type: string

env:
  AWS_REGION: us-east-1
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: infrastructure/package-lock.json

      - name: Install dependencies
        run: |
          cd infrastructure
          npm ci

      - name: Run tests
        run: |
          cd infrastructure
          npm test

      - name: Run linting
        run: |
          cd infrastructure
          npm run build

      - name: CDK Synth
        run: |
          cd infrastructure
          npx cdk synth --context environment=development --context domainName=test.hi-vi.com

  deploy-development:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: infrastructure/package-lock.json

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install dependencies
        run: |
          cd infrastructure
          npm ci

      - name: CDK Bootstrap
        run: |
          cd infrastructure
          npx cdk bootstrap

      - name: CDK Deploy Development
        run: |
          cd infrastructure
          npx cdk deploy HiViDevelopmentStack \
            --context environment=development \
            --context domainName=dev.hi-vi.com \
            --require-approval never

      - name: Get stack outputs
        id: outputs
        run: |
          cd infrastructure
          LOAD_BALANCER_DNS=$(aws cloudformation describe-stacks \
            --stack-name hivi-development \
            --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' \
            --output text)
          echo "load_balancer_dns=$LOAD_BALANCER_DNS" >> $GITHUB_OUTPUT

      - name: Comment PR with deployment info
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Development Infrastructure Deployed**

              **Load Balancer DNS:** ${{ steps.outputs.outputs.load_balancer_dns }}
              **API URL:** https://api.dev.hi-vi.com

              The infrastructure has been successfully deployed to the development environment.`
            })

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: infrastructure/package-lock.json

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install dependencies
        run: |
          cd infrastructure
          npm ci

      - name: CDK Deploy Staging
        run: |
          cd infrastructure
          npx cdk deploy HiViStagingStack \
            --context environment=staging \
            --context domainName=${{ github.event.inputs.domain_name }} \
            --require-approval never

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: infrastructure/package-lock.json

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install dependencies
        run: |
          cd infrastructure
          npm ci

      - name: CDK Deploy Production
        run: |
          cd infrastructure
          npx cdk deploy HiViProductionStack \
            --context environment=production \
            --context domainName=${{ github.event.inputs.domain_name }} \
            --require-approval never

      - name: Notify deployment success
        run: |
          echo "🎉 Production infrastructure deployed successfully!"
          echo "API URL: https://api.${{ github.event.inputs.domain_name }}"

  security-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: infrastructure/package-lock.json

      - name: Install dependencies
        run: |
          cd infrastructure
          npm ci

      - name: Run security audit
        run: |
          cd infrastructure
          npm audit --audit-level moderate

      - name: CDK Security Check
        run: |
          cd infrastructure
          # Synthesize templates for security analysis
          npx cdk synth --context environment=development --context domainName=test.hi-vi.com

          # Check for common security issues
          echo "Checking for hardcoded secrets..."
          if grep -r "AKIA\|sk_live_\|sk_test_" cdk.out/; then
            echo "❌ Potential hardcoded secrets found!"
            exit 1
          else
            echo "✅ No hardcoded secrets detected"
          fi
