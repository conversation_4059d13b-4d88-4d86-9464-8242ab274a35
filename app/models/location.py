import uuid

from sqlmodel import Field, Relationship, SQLModel

from app.models.base import TimestampMixin


# Location model for coach locations
class Location(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    city: str = Field(max_length=100, index=True)
    state: str = Field(max_length=100, index=True)
    country: str = Field(max_length=100, index=True)

    # Relationships
    coach_profiles: list["CoachProfile"] = Relationship(back_populates="location")
