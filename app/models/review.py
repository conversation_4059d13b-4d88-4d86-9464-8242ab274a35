import uuid

from sqlmodel import Column, Field, Relationship, SQLModel, Text

from app.models.base import TimestampMixin


# Review model for coach ratings and feedback
class Review(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Foreign keys
    coach_profile_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)

    # Review content
    rating: int = Field(ge=1, le=5)
    comment: str | None = Field(default=None, sa_column=Column(Text))

    # Relationships
    coach_profile: "CoachProfile" = Relationship(back_populates="reviews")
    user: "User" = Relationship()
