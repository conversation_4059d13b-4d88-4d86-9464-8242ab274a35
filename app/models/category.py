import uuid

from sqlmodel import Field, Relationship, SQLModel

from app.models.base import TimestampMixin


# Category model for coach specializations
class Category(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str = Field(max_length=100, unique=True, index=True)
    description: str | None = Field(default=None, max_length=500)

    # Relationships
    coach_profiles: list["CoachProfile"] = Relationship(back_populates="category")
