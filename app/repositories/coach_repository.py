import uuid

from sqlmodel import Session, func, or_, select

from app.models.coach import Coach<PERSON>rofile
from app.models.user import User
from app.repositories.base_repository import BaseRepository
from app.schemas.coach import CoachProfileCreate, CoachProfileUpdate, CoachSearchFilters


class CoachRepository(
    BaseRepository[CoachPro<PERSON>le, CoachProfileCreate, CoachProfileUpdate]
):
    """Repository for CoachProfile model with coach-specific methods."""

    def __init__(self):
        super().__init__(CoachProfile)

    def get_by_user_id(
        self, session: Session, *, user_id: uuid.UUID
    ) -> CoachProfile | None:
        """Get coach profile by user ID."""
        statement = select(CoachProfile).where(CoachProfile.user_id == user_id)
        return session.exec(statement).first()

    def get_available_coaches(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[CoachProfile]:
        """Get only available coaches."""
        statement = (
            select(CoachProfile)
            .where(CoachProfile.is_available.is_(True))
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()

    def search_with_filters(
        self,
        session: Session,
        *,
        filters: CoachSearchFilters,
        skip: int = 0,
        limit: int = 100,
    ) -> list[CoachProfile]:
        """Search coaches with advanced filters."""
        statement = (
            select(CoachProfile)
            .join(User, CoachProfile.user_id == User.id)
            .where(CoachProfile.is_available.is_(True))
        )

        # Apply filters
        if filters.category_id:
            statement = statement.where(CoachProfile.category_id == filters.category_id)

        if filters.location_id:
            statement = statement.where(CoachProfile.location_id == filters.location_id)

        if filters.min_rating is not None:
            statement = statement.where(
                CoachProfile.average_rating >= filters.min_rating
            )

        if filters.max_hourly_rate is not None:
            statement = statement.where(
                CoachProfile.hourly_rate <= filters.max_hourly_rate
            )

        if filters.is_available is not None:
            statement = statement.where(
                CoachProfile.is_available == filters.is_available
            )

        if filters.search_query:
            # Search in user's full name and coach bio
            search_filter = or_(
                User.full_name.ilike(f"%{filters.search_query}%"),
                CoachProfile.bio.ilike(f"%{filters.search_query}%"),
            )
            statement = statement.where(search_filter)

        statement = statement.offset(skip).limit(limit)
        return session.exec(statement).all()

    def count_with_filters(
        self, session: Session, *, filters: CoachSearchFilters
    ) -> int:
        """Count coaches matching filters."""
        statement = (
            select(func.count(CoachProfile.id))
            .join(User, CoachProfile.user_id == User.id)
            .where(CoachProfile.is_available.is_(True))
        )

        # Apply same filters as search_with_filters
        if filters.category_id:
            statement = statement.where(CoachProfile.category_id == filters.category_id)

        if filters.location_id:
            statement = statement.where(CoachProfile.location_id == filters.location_id)

        if filters.min_rating is not None:
            statement = statement.where(
                CoachProfile.average_rating >= filters.min_rating
            )

        if filters.max_hourly_rate is not None:
            statement = statement.where(
                CoachProfile.hourly_rate <= filters.max_hourly_rate
            )

        if filters.is_available is not None:
            statement = statement.where(
                CoachProfile.is_available == filters.is_available
            )

        if filters.search_query:
            search_filter = or_(
                User.full_name.ilike(f"%{filters.search_query}%"),
                CoachProfile.bio.ilike(f"%{filters.search_query}%"),
            )
            statement = statement.where(search_filter)

        return session.exec(statement).one()

    def get_by_category(
        self,
        session: Session,
        *,
        category_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[CoachProfile]:
        """Get coaches by category."""
        statement = (
            select(CoachProfile)
            .where(
                CoachProfile.category_id == category_id,
                CoachProfile.is_available.is_(True),
            )
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()

    def get_by_location(
        self,
        session: Session,
        *,
        location_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[CoachProfile]:
        """Get coaches by location."""
        statement = (
            select(CoachProfile)
            .where(
                CoachProfile.location_id == location_id,
                CoachProfile.is_available.is_(True),
            )
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
