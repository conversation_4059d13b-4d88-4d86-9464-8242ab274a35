from sqlmodel import Session, select

from app.models.category import Category
from app.repositories.base_repository import BaseRepository


class CategoryRepository(BaseRepository[Category, dict, dict]):
    """Repository for Category model."""

    def __init__(self):
        super().__init__(Category)

    def get_by_name(self, session: Session, *, name: str) -> Category | None:
        """Get category by name."""
        statement = select(Category).where(Category.name == name)
        return session.exec(statement).first()

    def create_category(
        self, session: Session, *, name: str, description: str | None = None
    ) -> Category:
        """Create a new category."""
        db_obj = Category(name=name, description=description)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
