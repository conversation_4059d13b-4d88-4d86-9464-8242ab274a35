import uuid

from sqlmodel import Session

from app.models.review import Review
from app.repositories.review_repository import ReviewRepository
from app.schemas.review import ReviewCreate
from app.services.coach_service import CoachService


class ReviewService:
    """Business logic for review operations."""

    def __init__(self):
        self.review_repo = ReviewRepository()
        self.coach_service = CoachService()

    def create_review(
        self, session: Session, *, user_id: uuid.UUID, review_in: ReviewCreate
    ) -> Review:
        """Create a new review and update coach rating."""
        # Create the review
        review = self.review_repo.create_review(
            session, user_id=user_id, review_in=review_in
        )

        # Update coach's average rating
        self.coach_service.update_coach_rating(
            session, coach_profile_id=review_in.coach_profile_id
        )

        return review

    def get_reviews_for_coach(
        self,
        session: Session,
        *,
        coach_profile_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Review]:
        """Get reviews for a specific coach."""
        return self.review_repo.get_by_coach(
            session, coach_profile_id=coach_profile_id, skip=skip, limit=limit
        )

    def get_reviews_by_user(
        self, session: Session, *, user_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[Review]:
        """Get reviews by a specific user."""
        return self.review_repo.get_by_user(
            session, user_id=user_id, skip=skip, limit=limit
        )

    def get_coach_rating_stats(
        self, session: Session, *, coach_profile_id: uuid.UUID
    ) -> tuple[float | None, int]:
        """Get rating statistics for a coach."""
        return self.review_repo.get_rating_stats(
            session, coach_profile_id=coach_profile_id
        )
