import uuid

from sqlmodel import Session

from app.models.coach import Coach<PERSON><PERSON><PERSON>le
from app.repositories.category_repository import CategoryRepository
from app.repositories.coach_repository import CoachRepository
from app.repositories.location_repository import LocationRepository
from app.repositories.review_repository import ReviewRepository
from app.schemas.coach import CoachProfileCreate, CoachProfileUpdate, CoachSearchFilters


class CoachService:
    """Business logic for coach-related operations."""

    def __init__(self):
        self.coach_repo = CoachRepository()
        self.category_repo = CategoryRepository()
        self.location_repo = LocationRepository()
        self.review_repo = ReviewRepository()

    def search_coaches(
        self,
        session: Session,
        *,
        filters: CoachSearchFilters,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[list[CoachProfile], int]:
        """Search coaches with filters and return results with total count."""
        coaches = self.coach_repo.search_with_filters(
            session, filters=filters, skip=skip, limit=limit
        )
        total_count = self.coach_repo.count_with_filters(session, filters=filters)

        return coaches, total_count

    def get_coach_by_id(
        self, session: Session, *, coach_id: uuid.UUID
    ) -> CoachProfile | None:
        """Get coach profile by ID."""
        return self.coach_repo.get(session, coach_id)

    def get_coach_by_user_id(
        self, session: Session, *, user_id: uuid.UUID
    ) -> CoachProfile | None:
        """Get coach profile by user ID."""
        return self.coach_repo.get_by_user_id(session, user_id=user_id)

    def create_coach_profile(
        self, session: Session, *, user_id: uuid.UUID, coach_data: CoachProfileCreate
    ) -> CoachProfile:
        """Create a new coach profile."""
        # Validate category and location exist if provided
        if coach_data.category_id:
            category = self.category_repo.get(session, coach_data.category_id)
            if not category:
                raise ValueError("Invalid category ID")

        if coach_data.location_id:
            location = self.location_repo.get(session, coach_data.location_id)
            if not location:
                raise ValueError("Invalid location ID")

        # Create coach profile
        coach_profile_data = coach_data.model_dump()
        coach_profile_data["user_id"] = user_id

        db_coach = CoachProfile(**coach_profile_data)
        session.add(db_coach)
        session.commit()
        session.refresh(db_coach)

        return db_coach

    def update_coach_profile(
        self,
        session: Session,
        *,
        coach_profile: CoachProfile,
        coach_data: CoachProfileUpdate,
    ) -> CoachProfile:
        """Update an existing coach profile."""
        # Validate category and location exist if provided
        if coach_data.category_id:
            category = self.category_repo.get(session, coach_data.category_id)
            if not category:
                raise ValueError("Invalid category ID")

        if coach_data.location_id:
            location = self.location_repo.get(session, coach_data.location_id)
            if not location:
                raise ValueError("Invalid location ID")

        return self.coach_repo.update(session, db_obj=coach_profile, obj_in=coach_data)

    def update_coach_rating(
        self, session: Session, *, coach_profile_id: uuid.UUID
    ) -> None:
        """Update coach's average rating and total reviews count."""
        avg_rating, total_reviews = self.review_repo.get_rating_stats(
            session, coach_profile_id=coach_profile_id
        )

        coach_profile = self.coach_repo.get(session, coach_profile_id)
        if coach_profile:
            coach_profile.average_rating = avg_rating
            coach_profile.total_reviews = total_reviews
            session.add(coach_profile)
            session.commit()

    def get_available_coaches(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[CoachProfile]:
        """Get only available coaches."""
        return self.coach_repo.get_available_coaches(session, skip=skip, limit=limit)

    def get_coaches_by_category(
        self,
        session: Session,
        *,
        category_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[CoachProfile]:
        """Get coaches by category."""
        return self.coach_repo.get_by_category(
            session, category_id=category_id, skip=skip, limit=limit
        )

    def get_coaches_by_location(
        self,
        session: Session,
        *,
        location_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[CoachProfile]:
        """Get coaches by location."""
        return self.coach_repo.get_by_location(
            session, location_id=location_id, skip=skip, limit=limit
        )
