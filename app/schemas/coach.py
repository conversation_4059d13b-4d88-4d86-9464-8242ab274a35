import uuid
from datetime import datetime

from pydantic import Field
from sqlmodel import SQLModel

from app.schemas.common import PaginatedResponse
from app.schemas.user import UserPublic


class CategoryPublic(SQLModel):
    id: uuid.UUID
    name: str
    description: str | None


class LocationPublic(SQLModel):
    id: uuid.UUID
    city: str
    state: str
    country: str


class CoachProfilePublic(SQLModel):
    id: uuid.UUID
    user_id: uuid.UUID
    bio: str | None
    experience_years: int | None
    hourly_rate: float | None
    is_available: bool
    profile_image_url: str | None
    average_rating: float | None
    total_reviews: int
    created_at: datetime

    # Related data
    user: UserPublic
    category: CategoryPublic | None
    location: LocationPublic | None


class CoachProfileCreate(SQLModel):
    bio: str | None = None
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool = True
    profile_image_url: str | None = None
    category_id: uuid.UUID | None = None
    location_id: uuid.UUID | None = None


class CoachProfileUpdate(SQLModel):
    bio: str | None = None
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool | None = None
    profile_image_url: str | None = None
    category_id: uuid.UUID | None = None
    location_id: uuid.UUID | None = None


class CoachesPublic(PaginatedResponse):
    data: list[CoachProfilePublic]


class CategoriesPublic(PaginatedResponse):
    data: list[CategoryPublic]


class LocationsPublic(PaginatedResponse):
    data: list[LocationPublic]


# Search and filter schemas
class CoachSearchFilters(SQLModel):
    category_id: uuid.UUID | None = None
    location_id: uuid.UUID | None = None
    min_rating: float | None = Field(default=None, ge=0, le=5)
    max_hourly_rate: float | None = Field(default=None, ge=0)
    search_query: str | None = None
    is_available: bool | None = True
