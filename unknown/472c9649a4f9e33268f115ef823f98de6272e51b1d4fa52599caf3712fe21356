from sqlmodel import Session, select

from app.models.user import User
from app.repositories.base_repository import BaseRepository
from app.schemas.user import User<PERSON>reate, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """Repository for User model with additional user-specific methods."""

    def __init__(self):
        super().__init__(User)

    def get_by_email(self, session: Session, *, email: str) -> User | None:
        """Get user by email address."""
        statement = select(User).where(User.email == email)
        return session.exec(statement).first()

    def get_by_role(
        self, session: Session, *, role: str, skip: int = 0, limit: int = 100
    ) -> list[User]:
        """Get users by role."""
        statement = select(User).where(User.role == role).offset(skip).limit(limit)
        return session.exec(statement).all()

    def get_active_users(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[User]:
        """Get active users only."""
        statement = (
            select(User).where(User.is_active.is_(True)).offset(skip).limit(limit)
        )
        return session.exec(statement).all()

    def create_with_hashed_password(
        self, session: Session, *, user_create: UserCreate, hashed_password: str
    ) -> User:
        """Create user with pre-hashed password."""
        user_data = user_create.model_dump()
        user_data.pop("password", None)  # Remove plain password
        user_data["hashed_password"] = hashed_password

        db_user = User(**user_data)
        session.add(db_user)
        session.commit()
        session.refresh(db_user)
        return db_user
