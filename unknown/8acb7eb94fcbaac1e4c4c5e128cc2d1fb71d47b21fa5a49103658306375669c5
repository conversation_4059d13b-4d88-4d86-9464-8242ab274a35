import uuid

from sqlmodel import Session

from app.models.category import Category
from app.repositories.category_repository import CategoryRepository


class CategoryService:
    """Business logic for category operations."""

    def __init__(self):
        self.category_repo = CategoryRepository()

    def get_all_categories(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Category]:
        """Get all categories."""
        return self.category_repo.get_multi(session, skip=skip, limit=limit)

    def get_category_by_id(
        self, session: Session, *, category_id: uuid.UUID
    ) -> Category | None:
        """Get category by ID."""
        return self.category_repo.get(session, category_id)

    def get_category_by_name(self, session: Session, *, name: str) -> Category | None:
        """Get category by name."""
        return self.category_repo.get_by_name(session, name=name)

    def create_category(
        self, session: Session, *, name: str, description: str | None = None
    ) -> Category:
        """Create a new category."""
        # Check if category already exists
        existing = self.category_repo.get_by_name(session, name=name)
        if existing:
            raise ValueError(f"Category '{name}' already exists")

        return self.category_repo.create_category(
            session, name=name, description=description
        )
