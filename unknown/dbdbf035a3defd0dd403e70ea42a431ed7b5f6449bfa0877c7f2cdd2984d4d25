from sqlmodel import Session

from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.repositories.user_repository import UserRepository
from app.schemas.user import UserCreate, UserUpdate


class AuthService:
    """Business logic for authentication and user management."""

    def __init__(self):
        self.user_repo = UserRepository()

    def authenticate_user(
        self, session: Session, *, email: str, password: str
    ) -> User | None:
        """Authenticate user with email and password."""
        user = self.user_repo.get_by_email(session, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    def create_user(self, session: Session, *, user_create: UserCreate) -> User:
        """Create a new user with hashed password."""
        hashed_password = get_password_hash(user_create.password)
        return self.user_repo.create_with_hashed_password(
            session, user_create=user_create, hashed_password=hashed_password
        )

    def get_user_by_email(self, session: Session, *, email: str) -> User | None:
        """Get user by email."""
        return self.user_repo.get_by_email(session, email=email)

    def get_user_by_id(self, session: Session, *, user_id: str) -> User | None:
        """Get user by ID."""
        return self.user_repo.get(session, user_id)

    def update_user(
        self, session: Session, *, db_user: User, user_in: UserUpdate
    ) -> User:
        """Update user information."""
        user_data = user_in.model_dump(exclude_unset=True)
        extra_data = {}

        if "password" in user_data:
            password = user_data["password"]
            hashed_password = get_password_hash(password)
            extra_data["hashed_password"] = hashed_password
            user_data.pop("password")

        return self.user_repo.update(
            session, db_obj=db_user, obj_in={**user_data, **extra_data}
        )

    def get_users_by_role(
        self, session: Session, *, role: str, skip: int = 0, limit: int = 100
    ) -> list[User]:
        """Get users by role."""
        return self.user_repo.get_by_role(session, role=role, skip=skip, limit=limit)

    def get_active_users(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[User]:
        """Get active users only."""
        return self.user_repo.get_active_users(session, skip=skip, limit=limit)
