import uuid

from sqlmodel import Session, select

from app.models.category import Category


class CategoryService:
    """Business logic for category operations."""

    def get_all_categories(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Category]:
        """Get all categories."""
        statement = select(Category).offset(skip).limit(limit)
        return session.exec(statement).all()

    def get_category_by_id(
        self, session: Session, *, category_id: uuid.UUID
    ) -> Category | None:
        """Get category by ID."""
        statement = select(Category).where(Category.id == category_id)
        return session.exec(statement).first()

    def create_category(
        self, session: Session, *, name: str, description: str | None = None
    ) -> Category:
        """Create a new category."""
        db_obj = Category(name=name, description=description)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
