#!/usr/bin/env python3
"""
Test script to verify event system is working correctly.
"""

import os
import sys
import uuid
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.event import Event, EventRSVP, EventPayment, EventStatus, EventType, RSVPStatus
from app.schemas.event import (
    EventCreate,
    EventUpdate,
    EventPublic,
    EventSearchFilters,
    RSVPCreate,
    EventStripeCheckoutRequest
)


def test_event_models():
    """Test that event models can be created."""
    try:
        # Test Event model
        event = Event(
            title="Spiritual Awakening Circle",
            description="A transformative group session for spiritual growth",
            host_id=uuid.uuid4(),
            event_date=datetime.utcnow() + timedelta(days=7),
            duration=120,
            capacity=15,
            price=75.0,
            event_type=EventType.ONLINE.value,
            meeting_link="https://zoom.us/j/123456789",
            category="Spiritual Growth",
            tags="meditation, awakening, transformation"
        )
        
        print("✅ Event model created successfully")
        print(f"   Event: {event.title}")
        print(f"   Type: {event.event_type}")
        print(f"   Capacity: {event.capacity}")
        print(f"   Price: ${event.price}")
        
        # Test EventRSVP model
        rsvp = EventRSVP(
            event_id=event.id,
            user_id=uuid.uuid4(),
            registration_notes="Looking forward to this transformative experience!",
            dietary_restrictions="Vegetarian"
        )
        
        print("✅ EventRSVP model created successfully")
        print(f"   RSVP ID: {rsvp.id}")
        print(f"   Status: {rsvp.status}")
        print(f"   Payment Required: {rsvp.payment_required}")
        
        # Test EventPayment model
        payment = EventPayment(
            rsvp_id=rsvp.id,
            user_id=rsvp.user_id,
            event_id=event.id,
            amount=event.price,
            description=f"Payment for {event.title}"
        )
        
        print("✅ EventPayment model created successfully")
        print(f"   Payment ID: {payment.id}")
        print(f"   Amount: ${payment.amount}")
        print(f"   Status: {payment.status}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create event models: {e}")
        return False


def test_event_schemas():
    """Test that event schemas work correctly."""
    try:
        # Test EventCreate schema
        event_create = EventCreate(
            title="Energy Healing Workshop",
            description="Learn powerful energy healing techniques",
            event_date=datetime.utcnow() + timedelta(days=14),
            duration=180,
            capacity=20,
            price=100.0,
            event_type=EventType.IN_PERSON,
            address="123 Spiritual Center, Healing City, HC 12345",
            category="Energy Healing",
            what_to_bring="Yoga mat, water bottle, open mind",
            prerequisites="Basic meditation experience recommended"
        )
        print("✅ EventCreate schema working")
        
        # Test EventUpdate schema
        event_update = EventUpdate(
            title="Advanced Energy Healing Workshop",
            price=120.0,
            capacity=25
        )
        print("✅ EventUpdate schema working")
        
        # Test RSVPCreate schema
        rsvp_create = RSVPCreate(
            event_id=uuid.uuid4(),
            registration_notes="Excited to learn energy healing!",
            dietary_restrictions="Gluten-free"
        )
        print("✅ RSVPCreate schema working")
        
        # Test EventSearchFilters schema
        search_filters = EventSearchFilters(
            event_type=EventType.ONLINE,
            category="Meditation",
            min_price=0.0,
            max_price=50.0,
            is_free=False,
            has_spots_available=True
        )
        print("✅ EventSearchFilters schema working")
        
        # Test EventStripeCheckoutRequest schema
        checkout_request = EventStripeCheckoutRequest(
            rsvp_id=uuid.uuid4(),
            success_url="https://example.com/success",
            cancel_url="https://example.com/cancel"
        )
        print("✅ EventStripeCheckoutRequest schema working")
        
        return True
    except Exception as e:
        print(f"❌ Failed to test event schemas: {e}")
        return False


def test_event_services_import():
    """Test that event services can be imported."""
    try:
        from app.services.event_service import EventService
        event_service = EventService()
        
        # Check if main methods exist
        methods = [
            'create_event',
            'update_event',
            'delete_event',
            'search_events',
            'get_event_by_id',
            'create_rsvp',
            'cancel_rsvp',
            'get_user_rsvps',
            'get_event_rsvps'
        ]
        
        for method in methods:
            if hasattr(event_service, method):
                print(f"✅ EventService.{method} method available")
            else:
                print(f"❌ EventService.{method} method missing")
                return False
        
        # Test EventStripeService import
        from app.services.event_stripe_service import EventStripeService
        stripe_service = EventStripeService()
        
        stripe_methods = [
            'create_checkout_session',
            'handle_webhook_event',
            'refund_payment',
            'get_payment_status'
        ]
        
        for method in stripe_methods:
            if hasattr(stripe_service, method):
                print(f"✅ EventStripeService.{method} method available")
            else:
                print(f"❌ EventStripeService.{method} method missing")
                return False
        
        print("✅ Event services imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import event services: {e}")
        return False


def test_event_repositories_import():
    """Test that event repositories can be imported."""
    try:
        from app.repositories.event_repository import EventRepository, EventRSVPRepository
        
        event_repo = EventRepository()
        rsvp_repo = EventRSVPRepository()
        
        # Check EventRepository methods
        event_methods = [
            'get_by_host',
            'search_events',
            'get_upcoming_events',
            'update_attendee_count'
        ]
        
        for method in event_methods:
            if hasattr(event_repo, method):
                print(f"✅ EventRepository.{method} method available")
            else:
                print(f"❌ EventRepository.{method} method missing")
                return False
        
        # Check EventRSVPRepository methods
        rsvp_methods = [
            'get_by_user_and_event',
            'get_by_event',
            'promote_from_waitlist'
        ]
        
        for method in rsvp_methods:
            if hasattr(rsvp_repo, method):
                print(f"✅ EventRSVPRepository.{method} method available")
            else:
                print(f"❌ EventRSVPRepository.{method} method missing")
                return False
        
        print("✅ Event repositories imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import event repositories: {e}")
        return False


def test_event_routes_import():
    """Test that event routes can be imported."""
    try:
        from app.api.routes.events import router
        
        # Check if router has the expected routes
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/events",
            "/events/upcoming",
            "/events/{event_id}",
            "/my-events",
            "/events/{event_id}/rsvp",
            "/events/{event_id}/rsvps",
            "/my-rsvps",
            "/rsvps/{rsvp_id}",
            "/rsvps/{rsvp_id}/checkout",
            "/rsvps/{rsvp_id}/payment-status"
        ]
        
        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✅ Event route {expected_route} available")
            else:
                print(f"❌ Event route {expected_route} missing")
                return False
        
        print("✅ Event routes imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import event routes: {e}")
        return False


def test_enum_values():
    """Test that event enums have correct values."""
    try:
        # Test EventType enum
        assert EventType.ONLINE.value == "online"
        assert EventType.IN_PERSON.value == "in_person"
        assert EventType.HYBRID.value == "hybrid"
        print("✅ EventType enum values correct")
        
        # Test EventStatus enum
        assert EventStatus.DRAFT.value == "draft"
        assert EventStatus.PUBLISHED.value == "published"
        assert EventStatus.CANCELLED.value == "cancelled"
        assert EventStatus.COMPLETED.value == "completed"
        print("✅ EventStatus enum values correct")
        
        # Test RSVPStatus enum
        assert RSVPStatus.PENDING.value == "pending"
        assert RSVPStatus.CONFIRMED.value == "confirmed"
        assert RSVPStatus.CANCELLED.value == "cancelled"
        assert RSVPStatus.ATTENDED.value == "attended"
        assert RSVPStatus.NO_SHOW.value == "no_show"
        print("✅ RSVPStatus enum values correct")
        
        return True
    except Exception as e:
        print(f"❌ Failed to test enum values: {e}")
        return False


def main():
    """Run all event system tests."""
    print("🎪 Testing Event System for Group Sessions and Circles")
    print("=" * 70)
    
    tests = [
        test_event_models,
        test_event_schemas,
        test_enum_values,
        test_event_services_import,
        test_event_repositories_import,
        test_event_routes_import,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 50)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All event system tests passed!")
        print("\n✨ Your event system is working perfectly!")
        print("\n📋 Available Event Features:")
        print("   ✅ Event creation and management (coaches)")
        print("   ✅ Online and in-person event types")
        print("   ✅ Event search and filtering")
        print("   ✅ RSVP system with waitlist support")
        print("   ✅ Stripe payment integration")
        print("   ✅ Automatic capacity management")
        print("   ✅ Event categories and tags")
        print("   ✅ Registration deadlines and approval")
        print("   ✅ Comprehensive event analytics")
        print("\n🚀 Ready for coaches to host group sessions and spiritual circles!")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
