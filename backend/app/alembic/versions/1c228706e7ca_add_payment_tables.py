"""add_payment_tables

Revision ID: 1c228706e7ca
Revises: 9030b4b7caae
Create Date: 2025-06-01 10:34:04.592579

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '1c228706e7ca'
down_revision = '9030b4b7caae'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stripewebhookevent',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('stripe_event_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('event_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('processed', sa.Bo<PERSON>an(), nullable=False),
    sa.Column('processing_error', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('event_data', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('processed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('stripe_event_id')
    )
    op.create_table('payment',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('booking_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('payment_method', sa.String(length=20), nullable=False),
    sa.Column('stripe_payment_intent_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('stripe_checkout_session_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('stripe_customer_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('failure_reason', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('refund_amount', sa.Float(), nullable=True),
    sa.Column('refund_reason', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('paid_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['booking_id'], ['booking.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('booking_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('payment')
    op.drop_table('stripewebhookevent')
    # ### end Alembic commands ###
