"""add_spiritual_coach_models

Revision ID: 0bc8d9eb7b1f
Revises: 9ff88b92d7fc
Create Date: 2025-05-24 17:37:55.948231

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '0bc8d9eb7b1f'
down_revision = '9ff88b92d7fc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('category',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_category_name'), 'category', ['name'], unique=True)
    op.create_table('location',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('city', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('state', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_location_city'), 'location', ['city'], unique=False)
    op.create_index(op.f('ix_location_country'), 'location', ['country'], unique=False)
    op.create_index(op.f('ix_location_state'), 'location', ['state'], unique=False)
    op.create_table('coachprofile',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('experience_years', sa.Integer(), nullable=True),
    sa.Column('hourly_rate', sa.Float(), nullable=True),
    sa.Column('is_available', sa.Boolean(), nullable=False),
    sa.Column('profile_image_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('category_id', sa.Uuid(), nullable=True),
    sa.Column('location_id', sa.Uuid(), nullable=True),
    sa.Column('average_rating', sa.Float(), nullable=True),
    sa.Column('total_reviews', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['category.id'], ),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('review',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('coach_profile_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('rating', sa.Integer(), nullable=False),
    sa.Column('comment', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['coach_profile_id'], ['coachprofile.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('review')
    op.drop_table('coachprofile')
    op.drop_index(op.f('ix_location_state'), table_name='location')
    op.drop_index(op.f('ix_location_country'), table_name='location')
    op.drop_index(op.f('ix_location_city'), table_name='location')
    op.drop_table('location')
    op.drop_index(op.f('ix_category_name'), table_name='category')
    op.drop_table('category')
    # ### end Alembic commands ###
