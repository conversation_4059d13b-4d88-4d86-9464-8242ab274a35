"""add_booking_table

Revision ID: 9a12e15aa442
Revises: 2626eb376f6f
Create Date: 2025-06-01 09:20:16.410773

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9a12e15aa442'
down_revision = '2626eb376f6f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('booking',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('coach_id', sa.Uuid(), nullable=False),
    sa.Column('session_time', sa.DateTime(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('notes', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('session_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('meeting_link', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['coach_id'], ['coachprofile.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_booking_session_time'), 'booking', ['session_time'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_booking_session_time'), table_name='booking')
    op.drop_table('booking')
    # ### end Alembic commands ###
