"""add_timestamp_columns_to_all_tables

Revision ID: 2626eb376f6f
Revises: 0bc8d9eb7b1f
Create Date: 2025-05-29 22:55:04.928102

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '2626eb376f6f'
down_revision = '0bc8d9eb7b1f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add columns with default values first, then make them NOT NULL
    import datetime
    default_time = datetime.datetime.utcnow()

    # Add nullable columns with default values
    op.add_column('category', sa.Column('created_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('category', sa.Column('updated_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('item', sa.Column('created_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('item', sa.Column('updated_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('location', sa.Column('created_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('location', sa.Column('updated_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('user', sa.Column('created_at', sa.DateTime(), nullable=True, default=default_time))
    op.add_column('user', sa.Column('updated_at', sa.DateTime(), nullable=True, default=default_time))

    # Update existing rows with current timestamp
    op.execute(f"UPDATE category SET created_at = '{default_time}', updated_at = '{default_time}' WHERE created_at IS NULL")
    op.execute(f"UPDATE item SET created_at = '{default_time}', updated_at = '{default_time}' WHERE created_at IS NULL")
    op.execute(f"UPDATE location SET created_at = '{default_time}', updated_at = '{default_time}' WHERE created_at IS NULL")
    op.execute(f"UPDATE \"user\" SET created_at = '{default_time}', updated_at = '{default_time}' WHERE created_at IS NULL")

    # Now make columns NOT NULL
    op.alter_column('category', 'created_at', nullable=False)
    op.alter_column('category', 'updated_at', nullable=False)
    op.alter_column('item', 'created_at', nullable=False)
    op.alter_column('item', 'updated_at', nullable=False)
    op.alter_column('location', 'created_at', nullable=False)
    op.alter_column('location', 'updated_at', nullable=False)
    op.alter_column('user', 'created_at', nullable=False)
    op.alter_column('user', 'updated_at', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'updated_at')
    op.drop_column('user', 'created_at')
    op.drop_column('location', 'updated_at')
    op.drop_column('location', 'created_at')
    op.drop_column('item', 'updated_at')
    op.drop_column('item', 'created_at')
    op.drop_column('category', 'updated_at')
    op.drop_column('category', 'created_at')
    # ### end Alembic commands ###
