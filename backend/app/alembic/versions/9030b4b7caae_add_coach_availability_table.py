"""add_coach_availability_table

Revision ID: 9030b4b7caae
Revises: 9a12e15aa442
Create Date: 2025-06-01 10:18:34.141657

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9030b4b7caae'
down_revision = '9a12e15aa442'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('coachavailability',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('coach_id', sa.Uuid(), nullable=False),
    sa.Column('day_of_week', sa.String(length=10), nullable=False),
    sa.Column('start_time', sa.Time(), nullable=False),
    sa.Column('end_time', sa.Time(), nullable=False),
    sa.Column('availability_type', sa.String(length=20), nullable=False),
    sa.Column('specific_date', sa.DateTime(), nullable=True),
    sa.Column('timezone', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('notes', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['coach_id'], ['coachprofile.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('coachavailability')
    # ### end Alembic commands ###
