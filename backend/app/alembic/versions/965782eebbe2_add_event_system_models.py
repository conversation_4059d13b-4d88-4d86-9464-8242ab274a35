"""add_event_system_models

Revision ID: 965782eebbe2
Revises: 8e4ce7cfe0d7
Create Date: 2025-06-01 17:29:12.434265

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '965782eebbe2'
down_revision = '8e4ce7cfe0d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('event',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('host_id', sa.Uuid(), nullable=False),
    sa.Column('event_date', sa.DateTime(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=False),
    sa.Column('capacity', sa.Integer(), nullable=False),
    sa.Column('current_attendees', sa.Integer(), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=False),
    sa.Column('event_type', sa.String(length=20), nullable=False),
    sa.Column('location_id', sa.Uuid(), nullable=True),
    sa.Column('address', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('meeting_link', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('meeting_password', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('registration_deadline', sa.DateTime(), nullable=True),
    sa.Column('allow_waitlist', sa.Boolean(), nullable=False),
    sa.Column('require_approval', sa.Boolean(), nullable=False),
    sa.Column('category', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('tags', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('what_to_bring', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('prerequisites', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['host_id'], ['coachprofile.id'], ),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_event_event_date'), 'event', ['event_date'], unique=False)
    op.create_table('eventrsvp',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('payment_required', sa.Boolean(), nullable=False),
    sa.Column('payment_completed', sa.Boolean(), nullable=False),
    sa.Column('amount_paid', sa.Float(), nullable=False),
    sa.Column('stripe_checkout_session_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('stripe_payment_intent_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('registration_notes', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('dietary_restrictions', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('is_waitlisted', sa.Boolean(), nullable=False),
    sa.Column('waitlist_position', sa.Integer(), nullable=True),
    sa.Column('checked_in', sa.Boolean(), nullable=False),
    sa.Column('check_in_time', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sqlite_autoincrement=True
    )
    op.create_table('eventpayment',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('rsvp_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('currency', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('stripe_checkout_session_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('stripe_payment_intent_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('refund_amount', sa.Float(), nullable=True),
    sa.Column('refund_reason', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('paid_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], ),
    sa.ForeignKeyConstraint(['rsvp_id'], ['eventrsvp.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('rsvp_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('eventpayment')
    op.drop_table('eventrsvp')
    op.drop_index(op.f('ix_event_event_date'), table_name='event')
    op.drop_table('event')
    # ### end Alembic commands ###
