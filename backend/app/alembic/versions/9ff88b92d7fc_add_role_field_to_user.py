"""add_role_field_to_user

Revision ID: 9ff88b92d7fc
Revises: 1a31ce608336
Create Date: 2025-05-22 15:09:51.581881

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '9ff88b92d7fc'
down_revision = '1a31ce608336'
branch_labels = None
depends_on = None


def upgrade():
    # Add role column as string with check constraint
    op.add_column('user', sa.Column('role', sa.String(50), nullable=False, server_default='user'))

    # Add check constraint to ensure valid values
    op.create_check_constraint(
        'user_role_check',
        'user',
        "role IN ('user', 'coach', 'admin')"
    )


def downgrade():
    # Drop the column (this will automatically drop any constraints on it)
    op.drop_column('user', 'role')

    # Drop the enum type if it exists (from the original migration)
    op.execute("DROP TYPE IF EXISTS userrole")

    # ### end Alembic commands ###
