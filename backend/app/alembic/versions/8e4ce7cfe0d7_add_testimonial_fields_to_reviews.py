"""add_testimonial_fields_to_reviews

Revision ID: 8e4ce7cfe0d7
Revises: 1c228706e7ca
Create Date: 2025-06-01 16:13:23.902801

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '8e4ce7cfe0d7'
down_revision = '1c228706e7ca'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('review', sa.Column('is_featured', sa.<PERSON>(), nullable=False))
    op.add_column('review', sa.Column('is_public', sa.<PERSON>(), nullable=False))
    op.add_column('review', sa.Column('session_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('review', 'session_type')
    op.drop_column('review', 'is_public')
    op.drop_column('review', 'is_featured')
    # ### end Alembic commands ###
