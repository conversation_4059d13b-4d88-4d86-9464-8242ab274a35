import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import Text
from sqlmodel import Column, Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.category import Category
    from app.models.coach_availability import CoachAvailability
    from app.models.location import Location
    from app.models.review import Review
    from app.models.user import User


# Coach profile model (extends User for coaches)
class CoachProfile(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", unique=True, nullable=False)

    # Profile information
    bio: str | None = Field(default=None, sa_column=Column(Text))
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool = Field(default=True)
    profile_image_url: str | None = Field(default=None, max_length=500)

    # Foreign keys
    category_id: uuid.UUID | None = Field(default=None, foreign_key="category.id")
    location_id: uuid.UUID | None = Field(default=None, foreign_key="location.id")

    # Calculated fields
    average_rating: float | None = Field(default=None, ge=0, le=5)
    total_reviews: int = Field(default=0, ge=0)

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    user: "User" = Relationship()
    category: Optional["Category"] = Relationship(back_populates="coach_profiles")
    location: Optional["Location"] = Relationship(back_populates="coach_profiles")
    reviews: list["Review"] = Relationship(back_populates="coach_profile")
    availability_slots: list["CoachAvailability"] = Relationship(back_populates="coach")
