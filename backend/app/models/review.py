import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from sqlmodel import Column, Field, Relationship, SQLModel, Text

if TYPE_CHECKING:
    from app.models.coach import CoachProfile
    from app.models.user import User


# Review/Testimonial model for coach ratings and feedback
class Review(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Foreign keys
    coach_profile_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)

    # Review content
    rating: int = Field(ge=1, le=5, description="Rating from 1-5 stars")
    comment: str | None = Field(
        default=None,
        sa_column=Column(Text),
        description="Detailed review/testimonial comment"
    )

    # Testimonial features
    is_featured: bool = Field(
        default=False,
        description="Whether this review is featured as a testimonial"
    )
    is_public: bool = Field(
        default=True,
        description="Whether this review is publicly visible"
    )

    # Session context (optional)
    session_type: str | None = Field(
        default=None,
        max_length=100,
        description="Type of session this review is for"
    )

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    coach_profile: "CoachProfile" = Relationship(back_populates="reviews")
    user: "User" = Relationship()
