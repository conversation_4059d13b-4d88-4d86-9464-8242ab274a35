import enum
import uuid
from datetime import datetime

from pydantic import EmailStr
from sqlmodel import CheckConstraint, Column, Field, SQLModel, String


class UserRole(str, enum.Enum):
    USER = "user"
    COACH = "coach"
    ADMIN = "admin"


# Database model, database table inferred from class name
class User(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    hashed_password: str
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Use string type with check constraint
    role: str = Field(
        default=UserRole.USER.value,
        sa_column=Column(
            String(50),
            CheckConstraint("role IN ('user', 'coach', 'admin')"),
            nullable=False,
        ),
    )
