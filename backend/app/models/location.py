import uuid
from datetime import datetime

from sqlmodel import Field, Relationship, SQLModel


# Location model for coach locations
class Location(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    city: str = Field(max_length=100, index=True)
    state: str = Field(max_length=100, index=True)
    country: str = Field(max_length=100, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    coach_profiles: list["CoachProfile"] = Relationship(back_populates="location")
