import enum
import uuid
from datetime import datetime, time
from typing import TYPE_CHECKING, Optional

from sqlmodel import Column, Field, Relationship, SQLModel, String

if TYPE_CHECKING:
    from app.models.coach import CoachProfile


class DayOfWeek(str, enum.Enum):
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"


class AvailabilityType(str, enum.Enum):
    RECURRING = "recurring"  # Weekly recurring availability
    EXCEPTION = "exception"  # One-time override (vacation, special hours)
    BLOCKED = "blocked"      # Blocked time (unavailable)


# Coach availability for defining working hours
class CoachAvailability(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Foreign key
    coach_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)
    
    # Availability details
    day_of_week: str = Field(
        sa_column=Column(String(10), nullable=False),
        description="Day of the week (monday, tuesday, etc.)"
    )
    start_time: time = Field(nullable=False, description="Start time for availability")
    end_time: time = Field(nullable=False, description="End time for availability")
    
    # Availability type and overrides
    availability_type: str = Field(
        default=AvailabilityType.RECURRING.value,
        sa_column=Column(String(20), nullable=False),
        description="Type of availability (recurring, exception, blocked)"
    )
    
    # For exceptions - specific date overrides
    specific_date: Optional[datetime] = Field(
        default=None,
        description="Specific date for exceptions (overrides recurring schedule)"
    )
    
    # Timezone support
    timezone: str = Field(
        default="UTC",
        max_length=50,
        description="Timezone for the availability (e.g., 'America/New_York')"
    )
    
    # Additional metadata
    is_active: bool = Field(default=True, description="Whether this availability is active")
    notes: Optional[str] = Field(default=None, max_length=500, description="Notes about this availability")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    coach: "CoachProfile" = Relationship(back_populates="availability_slots")


# Time slot model for representing available booking slots
class AvailableTimeSlot(SQLModel):
    """Represents an available time slot for booking (not a database table)"""
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    is_available: bool = True
    coach_id: uuid.UUID
    timezone: str = "UTC"
