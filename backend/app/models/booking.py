import enum
import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import Column, Field, Relationship, SQLModel, String

if TYPE_CHECKING:
    from app.models.coach import CoachProfile
    from app.models.payment import Payment
    from app.models.user import User


class BookingStatus(str, enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


# Booking model for coach session bookings
class Booking(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Foreign keys
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    coach_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)

    # Session details
    session_time: datetime = Field(nullable=False, index=True)
    duration: int = Field(ge=15, le=480, default=60)  # Duration in minutes (15min to 8hrs)

    # Booking status
    status: str = Field(
        default=BookingStatus.PENDING.value,
        sa_column=Column(
            String(20),
            nullable=False,
        ),
    )

    # Optional fields
    notes: Optional[str] = Field(default=None, max_length=1000)
    session_type: Optional[str] = Field(default=None, max_length=100)  # e.g., "Energy Healing", "Life Coaching"
    meeting_link: Optional[str] = Field(default=None, max_length=500)

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    user: "User" = Relationship()
    coach: "CoachProfile" = Relationship()
    payment: Optional["Payment"] = Relationship(back_populates="booking")
