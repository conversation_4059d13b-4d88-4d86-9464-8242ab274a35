import enum
import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import Column, Field, Relationship, SQLModel, String, Text

if TYPE_CHECKING:
    from app.models.coach import CoachProfile
    from app.models.location import Location
    from app.models.user import User


class EventType(str, enum.Enum):
    ONLINE = "online"
    IN_PERSON = "in_person"
    HYBRID = "hybrid"  # Both online and in-person options


class EventStatus(str, enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


class RSVPStatus(str, enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    ATTENDED = "attended"
    NO_SHOW = "no_show"


# Event model for group sessions and spiritual circles
class Event(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Basic event information
    title: str = Field(max_length=200, description="Event title")
    description: str = Field(sa_column=Column(Text), description="Detailed event description")
    
    # Host information
    host_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False, description="Coach hosting the event")
    
    # Event scheduling
    event_date: datetime = Field(nullable=False, index=True, description="Event date and time")
    duration: int = Field(ge=30, le=480, default=90, description="Event duration in minutes")
    
    # Capacity and pricing
    capacity: int = Field(ge=1, le=1000, default=20, description="Maximum number of participants")
    current_attendees: int = Field(default=0, ge=0, description="Current number of confirmed attendees")
    price: float = Field(ge=0, default=0.0, description="Event price per person (0 for free events)")
    currency: str = Field(default="usd", max_length=3, description="Currency code")
    
    # Event type and location
    event_type: str = Field(
        default=EventType.ONLINE.value,
        sa_column=Column(String(20), nullable=False),
        description="Event type (online, in_person, hybrid)"
    )
    
    # Location details (for in-person events)
    location_id: Optional[uuid.UUID] = Field(
        default=None, 
        foreign_key="location.id", 
        description="Location for in-person events"
    )
    address: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Specific address for in-person events"
    )
    
    # Online event details
    meeting_link: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Zoom/meeting link for online events"
    )
    meeting_password: Optional[str] = Field(
        default=None, 
        max_length=100, 
        description="Meeting password if required"
    )
    
    # Event status and settings
    status: str = Field(
        default=EventStatus.DRAFT.value,
        sa_column=Column(String(20), nullable=False),
        description="Event status"
    )
    
    # Registration settings
    registration_deadline: Optional[datetime] = Field(
        default=None, 
        description="Last date/time for registration"
    )
    allow_waitlist: bool = Field(
        default=True, 
        description="Allow waitlist when event is full"
    )
    require_approval: bool = Field(
        default=False, 
        description="Require host approval for registration"
    )
    
    # Event categories and tags
    category: Optional[str] = Field(
        default=None, 
        max_length=100, 
        description="Event category (e.g., Meditation Circle, Energy Healing)"
    )
    tags: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Comma-separated tags for the event"
    )
    
    # Additional information
    what_to_bring: Optional[str] = Field(
        default=None, 
        max_length=1000, 
        description="What participants should bring"
    )
    prerequisites: Optional[str] = Field(
        default=None, 
        max_length=1000, 
        description="Prerequisites or requirements"
    )
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    host: "CoachProfile" = Relationship()
    location: Optional["Location"] = Relationship()
    rsvps: list["EventRSVP"] = Relationship(back_populates="event")


# RSVP model for event registrations
class EventRSVP(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Foreign keys
    event_id: uuid.UUID = Field(foreign_key="event.id", nullable=False)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    
    # RSVP details
    status: str = Field(
        default=RSVPStatus.PENDING.value,
        sa_column=Column(String(20), nullable=False),
        description="RSVP status"
    )
    
    # Payment information
    payment_required: bool = Field(default=False, description="Whether payment is required")
    payment_completed: bool = Field(default=False, description="Whether payment has been completed")
    amount_paid: float = Field(default=0.0, ge=0, description="Amount paid for the event")
    
    # Stripe payment details
    stripe_checkout_session_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe checkout session ID"
    )
    stripe_payment_intent_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe payment intent ID"
    )
    
    # Registration details
    registration_notes: Optional[str] = Field(
        default=None, 
        max_length=1000, 
        description="Notes from participant during registration"
    )
    dietary_restrictions: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Dietary restrictions or special needs"
    )
    
    # Waitlist information
    is_waitlisted: bool = Field(default=False, description="Whether user is on waitlist")
    waitlist_position: Optional[int] = Field(
        default=None, 
        description="Position in waitlist"
    )
    
    # Attendance tracking
    checked_in: bool = Field(default=False, description="Whether participant checked in")
    check_in_time: Optional[datetime] = Field(
        default=None, 
        description="When participant checked in"
    )
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    event: "Event" = Relationship(back_populates="rsvps")
    user: "User" = Relationship()
    
    # Unique constraint to prevent duplicate RSVPs
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )


# Event payment model (extends the existing payment system)
class EventPayment(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Foreign keys
    rsvp_id: uuid.UUID = Field(foreign_key="eventrsvp.id", nullable=False, unique=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    event_id: uuid.UUID = Field(foreign_key="event.id", nullable=False)
    
    # Payment details
    amount: float = Field(ge=0, description="Payment amount")
    currency: str = Field(default="usd", max_length=3, description="Currency code")
    
    # Payment status
    status: str = Field(
        default="pending",
        sa_column=Column(String(20), nullable=False),
        description="Payment status"
    )
    
    # Stripe integration
    stripe_checkout_session_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe checkout session ID"
    )
    stripe_payment_intent_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe payment intent ID"
    )
    
    # Payment metadata
    description: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Payment description"
    )
    
    # Refund information
    refund_amount: Optional[float] = Field(
        default=None, 
        ge=0, 
        description="Refunded amount if applicable"
    )
    refund_reason: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Reason for refund"
    )
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    paid_at: Optional[datetime] = Field(default=None, description="When payment was completed")
    
    # Relationships
    rsvp: "EventRSVP" = Relationship()
    user: "User" = Relationship()
    event: "Event" = Relationship()
