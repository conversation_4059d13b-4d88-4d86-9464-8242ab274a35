import enum
import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import Column, Field, Relationship, SQLModel, String

if TYPE_CHECKING:
    from app.models.booking import Booking
    from app.models.user import User


class PaymentStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class PaymentMethod(str, enum.Enum):
    STRIPE = "stripe"
    PAYPAL = "paypal"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"


# Payment model for tracking session payments
class Payment(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Foreign keys
    booking_id: uuid.UUID = Field(foreign_key="booking.id", nullable=False, unique=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    
    # Payment details
    amount: float = Field(ge=0, description="Payment amount in USD")
    currency: str = Field(default="usd", max_length=3, description="Currency code")
    
    # Payment status and method
    status: str = Field(
        default=PaymentStatus.PENDING.value,
        sa_column=Column(String(20), nullable=False),
        description="Payment status"
    )
    payment_method: str = Field(
        default=PaymentMethod.STRIPE.value,
        sa_column=Column(String(20), nullable=False),
        description="Payment method used"
    )
    
    # Stripe-specific fields
    stripe_payment_intent_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe Payment Intent ID"
    )
    stripe_checkout_session_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe Checkout Session ID"
    )
    stripe_customer_id: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Stripe Customer ID"
    )
    
    # Payment metadata
    description: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Payment description"
    )
    failure_reason: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Reason for payment failure"
    )
    
    # Refund information
    refund_amount: Optional[float] = Field(
        default=None, 
        ge=0, 
        description="Refunded amount if applicable"
    )
    refund_reason: Optional[str] = Field(
        default=None, 
        max_length=500, 
        description="Reason for refund"
    )
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    paid_at: Optional[datetime] = Field(default=None, description="When payment was completed")
    
    # Relationships
    booking: "Booking" = Relationship(back_populates="payment")
    user: "User" = Relationship()


# Stripe webhook event tracking
class StripeWebhookEvent(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    
    # Stripe event details
    stripe_event_id: str = Field(max_length=255, unique=True, description="Stripe event ID")
    event_type: str = Field(max_length=100, description="Stripe event type")
    
    # Processing status
    processed: bool = Field(default=False, description="Whether event was processed")
    processing_error: Optional[str] = Field(
        default=None, 
        max_length=1000, 
        description="Error during processing"
    )
    
    # Event data
    event_data: Optional[str] = Field(
        default=None, 
        description="JSON data from Stripe event"
    )
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = Field(default=None, description="When event was processed")
