import enum
import uuid
from datetime import datetime

from pydantic import EmailStr
from sqlmodel import (
    CheckConstraint,
    Column,
    Field,
    Relationship,
    SQLModel,
    String,
    Text,
)


class UserRole(str, enum.Enum):
    USER = "user"
    COACH = "coach"
    ADMIN = "admin"


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)
    role: UserRole = Field(default=UserRole.USER)


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)
    # Role is inherited from UserBase with default=UserRole.USER


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)
    role: UserRole = Field(default=UserRole.USER)


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)
    role: UserRole | None = Field(default=None)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    # Use string type with check constraint
    role: str = Field(
        default=UserRole.USER.value,
        sa_column=Column(
            String(50),
            CheckConstraint("role IN ('user', 'coach', 'admin')"),
            nullable=False,
        ),
    )
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)


# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title: str = Field(max_length=255)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


# Spiritual Coach Models


# Category model for coach specializations
class Category(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str = Field(max_length=100, unique=True, index=True)
    description: str | None = Field(default=None, max_length=500)

    # Relationships
    coach_profiles: list["CoachProfile"] = Relationship(back_populates="category")


# Location model for coach locations
class Location(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    city: str = Field(max_length=100, index=True)
    state: str = Field(max_length=100, index=True)
    country: str = Field(max_length=100, index=True)

    # Relationships
    coach_profiles: list["CoachProfile"] = Relationship(back_populates="location")


# Coach profile model (extends User for coaches)
class CoachProfile(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", unique=True, nullable=False)

    # Profile information
    bio: str | None = Field(default=None, sa_column=Column(Text))
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool = Field(default=True)
    profile_image_url: str | None = Field(default=None, max_length=500)

    # Foreign keys
    category_id: uuid.UUID | None = Field(default=None, foreign_key="category.id")
    location_id: uuid.UUID | None = Field(default=None, foreign_key="location.id")

    # Calculated fields
    average_rating: float | None = Field(default=None, ge=0, le=5)
    total_reviews: int = Field(default=0, ge=0)

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    user: User = Relationship()
    category: Category | None = Relationship(back_populates="coach_profiles")
    location: Location | None = Relationship(back_populates="coach_profiles")
    reviews: list["Review"] = Relationship(back_populates="coach_profile")


# Review model for coach ratings and feedback
class Review(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Foreign keys
    coach_profile_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)

    # Review content
    rating: int = Field(ge=1, le=5)
    comment: str | None = Field(default=None, sa_column=Column(Text))

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    coach_profile: CoachProfile = Relationship(back_populates="reviews")
    user: User = Relationship()


# API Models for Coach Browsing


class CategoryPublic(SQLModel):
    id: uuid.UUID
    name: str
    description: str | None


class LocationPublic(SQLModel):
    id: uuid.UUID
    city: str
    state: str
    country: str


class CoachProfilePublic(SQLModel):
    id: uuid.UUID
    user_id: uuid.UUID
    bio: str | None
    experience_years: int | None
    hourly_rate: float | None
    is_available: bool
    profile_image_url: str | None
    average_rating: float | None
    total_reviews: int
    created_at: datetime

    # Related data
    user: UserPublic
    category: CategoryPublic | None
    location: LocationPublic | None


class CoachProfileCreate(SQLModel):
    bio: str | None = None
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool = True
    profile_image_url: str | None = None
    category_id: uuid.UUID | None = None
    location_id: uuid.UUID | None = None


class CoachProfileUpdate(SQLModel):
    bio: str | None = None
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool | None = None
    profile_image_url: str | None = None
    category_id: uuid.UUID | None = None
    location_id: uuid.UUID | None = None


class ReviewPublic(SQLModel):
    id: uuid.UUID
    rating: int
    comment: str | None
    created_at: datetime
    user: UserPublic


class ReviewCreate(SQLModel):
    coach_profile_id: uuid.UUID
    rating: int = Field(ge=1, le=5)
    comment: str | None = None


class CoachesPublic(SQLModel):
    data: list[CoachProfilePublic]
    count: int


class CategoriesPublic(SQLModel):
    data: list[CategoryPublic]
    count: int


class LocationsPublic(SQLModel):
    data: list[LocationPublic]
    count: int
