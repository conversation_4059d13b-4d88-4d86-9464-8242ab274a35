import uuid
from datetime import datetime
from typing import Optional

from fastapi import HTT<PERSON><PERSON>xception
from sqlmodel import Session

from app.models.booking import Booking, BookingStatus
from app.repositories.booking_repository import BookingRepository
from app.repositories.coach_repository import CoachRepository
from app.schemas.booking import BookingCreate, BookingStatusUpdate, BookingUpdate


class BookingService:
    """Business logic for booking-related operations."""
    
    def __init__(self):
        self.booking_repo = BookingRepository()
        self.coach_repo = CoachRepository()
    
    def create_booking(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        booking_data: BookingCreate
    ) -> Booking:
        """Create a new booking with availability validation."""
        # Verify coach exists
        coach = self.coach_repo.get(session, booking_data.coach_id)
        if not coach:
            raise HTTPException(status_code=404, detail="Coach not found")
        
        # Check if coach is available
        if not coach.is_available:
            raise HTTPException(status_code=400, detail="Coach is not currently accepting bookings")
        
        # Check for scheduling conflicts
        is_available = self.booking_repo.check_coach_availability(
            session,
            coach_id=booking_data.coach_id,
            session_time=booking_data.session_time,
            duration=booking_data.duration
        )
        
        if not is_available:
            raise HTTPException(
                status_code=409, 
                detail="Coach is not available at the requested time"
            )
        
        # Create the booking
        booking_dict = booking_data.model_dump()
        booking_dict["user_id"] = user_id
        
        db_booking = Booking(**booking_dict)
        session.add(db_booking)
        session.commit()
        session.refresh(db_booking)
        
        return db_booking
    
    def get_user_bookings(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> tuple[list[Booking], int]:
        """Get user's bookings with pagination."""
        bookings = self.booking_repo.get_user_bookings(
            session,
            user_id=user_id,
            status=status,
            upcoming_only=upcoming_only,
            skip=skip,
            limit=limit
        )
        
        total_count = self.booking_repo.count_user_bookings(
            session,
            user_id=user_id,
            status=status,
            upcoming_only=upcoming_only
        )
        
        return bookings, total_count
    
    def get_coach_bookings(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> tuple[list[Booking], int]:
        """Get coach's bookings with pagination."""
        bookings = self.booking_repo.get_coach_bookings(
            session,
            coach_id=coach_id,
            status=status,
            upcoming_only=upcoming_only,
            skip=skip,
            limit=limit
        )
        
        total_count = self.booking_repo.count_coach_bookings(
            session,
            coach_id=coach_id,
            status=status,
            upcoming_only=upcoming_only
        )
        
        return bookings, total_count
    
    def update_booking(
        self,
        session: Session,
        *,
        booking_id: uuid.UUID,
        user_id: uuid.UUID,
        booking_data: BookingUpdate
    ) -> Booking:
        """Update a booking (only by the user who created it)."""
        booking = self.booking_repo.get(session, booking_id)
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")
        
        if booking.user_id != user_id:
            raise HTTPException(status_code=403, detail="Not authorized to update this booking")
        
        # If updating session time, check availability
        if booking_data.session_time and booking_data.session_time != booking.session_time:
            duration = booking_data.duration or booking.duration
            is_available = self.booking_repo.check_coach_availability(
                session,
                coach_id=booking.coach_id,
                session_time=booking_data.session_time,
                duration=duration,
                exclude_booking_id=booking_id
            )
            
            if not is_available:
                raise HTTPException(
                    status_code=409,
                    detail="Coach is not available at the requested time"
                )
        
        # Update the booking
        updated_booking = self.booking_repo.update(session, db_obj=booking, obj_in=booking_data)
        return updated_booking
    
    def update_booking_status(
        self,
        session: Session,
        *,
        booking_id: uuid.UUID,
        coach_user_id: uuid.UUID,
        status_data: BookingStatusUpdate
    ) -> Booking:
        """Update booking status (only by the coach)."""
        booking = self.booking_repo.get(session, booking_id)
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")
        
        # Verify the user is the coach for this booking
        coach = self.coach_repo.get(session, booking.coach_id)
        if not coach or coach.user_id != coach_user_id:
            raise HTTPException(status_code=403, detail="Not authorized to update this booking")
        
        # Update the booking status
        update_data = status_data.model_dump(exclude_unset=True)
        updated_booking = self.booking_repo.update(session, db_obj=booking, obj_in=update_data)
        return updated_booking
    
    def cancel_booking(
        self,
        session: Session,
        *,
        booking_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Booking:
        """Cancel a booking (by user or coach)."""
        booking = self.booking_repo.get(session, booking_id)
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")
        
        # Check if user is authorized (either the booking user or the coach)
        coach = self.coach_repo.get(session, booking.coach_id)
        is_booking_user = booking.user_id == user_id
        is_coach = coach and coach.user_id == user_id
        
        if not (is_booking_user or is_coach):
            raise HTTPException(status_code=403, detail="Not authorized to cancel this booking")
        
        # Update status to cancelled
        booking.status = BookingStatus.CANCELLED.value
        booking.updated_at = datetime.utcnow()
        session.add(booking)
        session.commit()
        session.refresh(booking)
        
        return booking
    
    def get_booking_by_id(
        self,
        session: Session,
        *,
        booking_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Booking:
        """Get a specific booking (only if user is authorized)."""
        booking = self.booking_repo.get(session, booking_id)
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")
        
        # Check if user is authorized (either the booking user or the coach)
        coach = self.coach_repo.get(session, booking.coach_id)
        is_booking_user = booking.user_id == user_id
        is_coach = coach and coach.user_id == user_id
        
        if not (is_booking_user or is_coach):
            raise HTTPException(status_code=403, detail="Not authorized to view this booking")
        
        return booking
