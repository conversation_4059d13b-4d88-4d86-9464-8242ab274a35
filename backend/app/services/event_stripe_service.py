import uuid
from datetime import datetime
from typing import Optional

import stripe
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from sqlmodel import Session, select

from app.core.config import settings
from app.models.event import EventPayment, EventRSVP, RSVPStatus
from app.repositories.event_repository import EventRSVPRepository
from app.schemas.event import EventStripeCheckoutRequest, EventStripeCheckoutResponse


class EventStripeService:
    """Stripe payment service for events."""

    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.rsvp_repo = EventRSVPRepository()

    def create_checkout_session(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        checkout_request: EventStripeCheckoutRequest
    ) -> EventStripeCheckoutResponse:
        """Create a Stripe checkout session for event payment."""
        # Get the RSVP
        rsvp = session.get(EventRSVP, checkout_request.rsvp_id)
        if not rsvp:
            raise HTTPException(status_code=404, detail="RSVP not found")

        # Verify user owns the RSVP
        if rsvp.user_id != user_id:
            raise HTTPException(status_code=403, detail="You can only pay for your own RSVP")

        # Check if payment is required
        if not rsvp.payment_required:
            raise HTTPException(status_code=400, detail="Payment is not required for this event")

        # Check if already paid
        if rsvp.payment_completed:
            raise HTTPException(status_code=400, detail="Payment has already been completed")

        # Get the event
        event = rsvp.event
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")

        # Create event payment record
        event_payment = EventPayment(
            rsvp_id=rsvp.id,
            user_id=user_id,
            event_id=event.id,
            amount=event.price,
            currency=event.currency,
            description=f"Event: {event.title}"
        )
        session.add(event_payment)
        session.commit()
        session.refresh(event_payment)

        try:
            # Create Stripe checkout session
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': event.currency,
                        'product_data': {
                            'name': event.title,
                            'description': f"Event on {event.event_date.strftime('%B %d, %Y at %I:%M %p')}",
                            'metadata': {
                                'event_id': str(event.id),
                                'rsvp_id': str(rsvp.id),
                                'event_payment_id': str(event_payment.id)
                            }
                        },
                        'unit_amount': int(event.price * 100),  # Convert to cents
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=checkout_request.success_url or f"{settings.FRONTEND_URL}/events/{event.id}/payment-success",
                cancel_url=checkout_request.cancel_url or f"{settings.FRONTEND_URL}/events/{event.id}",
                metadata={
                    'event_id': str(event.id),
                    'rsvp_id': str(rsvp.id),
                    'event_payment_id': str(event_payment.id),
                    'user_id': str(user_id)
                }
            )

            # Update payment record with Stripe session ID
            event_payment.stripe_checkout_session_id = checkout_session.id
            session.commit()

            # Update RSVP with Stripe session ID
            rsvp.stripe_checkout_session_id = checkout_session.id
            session.commit()

            return EventStripeCheckoutResponse(
                checkout_url=checkout_session.url,
                session_id=checkout_session.id,
                payment_id=event_payment.id
            )

        except stripe.error.StripeError as e:
            # Clean up payment record on Stripe error
            session.delete(event_payment)
            session.commit()
            raise HTTPException(status_code=400, detail=f"Payment processing error: {str(e)}")

    def handle_webhook_event(
        self,
        session: Session,
        *,
        event_type: str,
        stripe_event_data: dict
    ) -> bool:
        """Handle Stripe webhook events for event payments."""
        if event_type == "checkout.session.completed":
            return self._handle_checkout_completed(session, stripe_event_data)
        elif event_type == "payment_intent.succeeded":
            return self._handle_payment_succeeded(session, stripe_event_data)
        elif event_type == "payment_intent.payment_failed":
            return self._handle_payment_failed(session, stripe_event_data)

        return False

    def _handle_checkout_completed(
        self,
        session: Session,
        stripe_event_data: dict
    ) -> bool:
        """Handle successful checkout completion."""
        checkout_session = stripe_event_data.get("object", {})
        session_id = checkout_session.get("id")
        payment_intent_id = checkout_session.get("payment_intent")
        metadata = checkout_session.get("metadata", {})

        if not session_id:
            return False

        # Find the RSVP by Stripe session ID
        rsvp = session.exec(
            select(EventRSVP).where(
                EventRSVP.stripe_checkout_session_id == session_id
            )
        ).first()

        if not rsvp:
            return False

        # Update RSVP with payment intent ID
        rsvp.stripe_payment_intent_id = payment_intent_id

        # Find and update the event payment record
        event_payment = session.exec(
            select(EventPayment).where(
                EventPayment.rsvp_id == rsvp.id
            )
        ).first()

        if event_payment:
            event_payment.stripe_payment_intent_id = payment_intent_id
            event_payment.status = "processing"

        session.commit()
        return True

    def _handle_payment_succeeded(
        self,
        session: Session,
        stripe_event_data: dict
    ) -> bool:
        """Handle successful payment."""
        payment_intent = stripe_event_data.get("object", {})
        payment_intent_id = payment_intent.get("id")

        if not payment_intent_id:
            return False

        # Find the RSVP by payment intent ID
        rsvp = session.exec(
            select(EventRSVP).where(
                EventRSVP.stripe_payment_intent_id == payment_intent_id
            )
        ).first()

        if not rsvp:
            return False

        # Update RSVP payment status
        rsvp.payment_completed = True
        rsvp.amount_paid = rsvp.event.price

        # Confirm RSVP if it was pending payment
        if rsvp.status == RSVPStatus.PENDING.value:
            rsvp.status = RSVPStatus.CONFIRMED.value

        # Update event payment record
        event_payment = session.exec(
            select(EventPayment).where(
                EventPayment.rsvp_id == rsvp.id
            )
        ).first()

        if event_payment:
            event_payment.status = "succeeded"
            event_payment.paid_at = datetime.utcnow()

        session.commit()

        # Update event attendee count if not waitlisted
        if not rsvp.is_waitlisted:
            from app.repositories.event_repository import EventRepository
            event_repo = EventRepository()
            event_repo.update_attendee_count(session=session, event_id=rsvp.event_id)

        return True

    def _handle_payment_failed(
        self,
        session: Session,
        stripe_event_data: dict
    ) -> bool:
        """Handle failed payment."""
        payment_intent = stripe_event_data.get("object", {})
        payment_intent_id = payment_intent.get("id")

        if not payment_intent_id:
            return False

        # Find the RSVP by payment intent ID
        rsvp = session.exec(
            select(EventRSVP).where(
                EventRSVP.stripe_payment_intent_id == payment_intent_id
            )
        ).first()

        if not rsvp:
            return False

        # Update event payment record
        event_payment = session.exec(
            select(EventPayment).where(
                EventPayment.rsvp_id == rsvp.id
            )
        ).first()

        if event_payment:
            event_payment.status = "failed"

        session.commit()
        return True

    def refund_payment(
        self,
        session: Session,
        *,
        rsvp_id: uuid.UUID,
        amount: Optional[float] = None,
        reason: str = "requested_by_customer"
    ) -> bool:
        """Refund an event payment."""
        rsvp = session.get(EventRSVP, rsvp_id)
        if not rsvp or not rsvp.payment_completed:
            raise HTTPException(status_code=400, detail="No completed payment found for this RSVP")

        if not rsvp.stripe_payment_intent_id:
            raise HTTPException(status_code=400, detail="No Stripe payment intent found")

        try:
            # Create refund in Stripe
            refund_amount = amount or rsvp.amount_paid
            refund = stripe.Refund.create(
                payment_intent=rsvp.stripe_payment_intent_id,
                amount=int(refund_amount * 100),  # Convert to cents
                reason=reason
            )

            # Update event payment record
            event_payment = session.exec(
                select(EventPayment).where(
                    EventPayment.rsvp_id == rsvp.id
                )
            ).first()

            if event_payment:
                event_payment.refund_amount = refund_amount
                event_payment.refund_reason = reason
                event_payment.status = "refunded"

            # Update RSVP
            rsvp.payment_completed = False
            rsvp.amount_paid = max(0, rsvp.amount_paid - refund_amount)
            rsvp.status = RSVPStatus.CANCELLED.value

            session.commit()

            # Update event attendee count
            from app.repositories.event_repository import EventRepository
            event_repo = EventRepository()
            event_repo.update_attendee_count(session=session, event_id=rsvp.event_id)

            return True

        except stripe.error.StripeError as e:
            raise HTTPException(status_code=400, detail=f"Refund failed: {str(e)}")

    def get_payment_status(
        self,
        session: Session,
        *,
        rsvp_id: uuid.UUID
    ) -> dict:
        """Get payment status for an RSVP."""
        rsvp = session.get(EventRSVP, rsvp_id)
        if not rsvp:
            raise HTTPException(status_code=404, detail="RSVP not found")

        event_payment = session.exec(
            select(EventPayment).where(
                EventPayment.rsvp_id == rsvp.id
            )
        ).first()

        return {
            "rsvp_id": rsvp.id,
            "payment_required": rsvp.payment_required,
            "payment_completed": rsvp.payment_completed,
            "amount_paid": rsvp.amount_paid,
            "payment_status": event_payment.status if event_payment else None,
            "stripe_session_id": rsvp.stripe_checkout_session_id,
            "stripe_payment_intent_id": rsvp.stripe_payment_intent_id
        }
