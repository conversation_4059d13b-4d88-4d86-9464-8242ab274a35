import uuid
from typing import Optional

from sqlmodel import Session, and_, func, select

from app.models.review import Review
from app.schemas.review import Review<PERSON>reate, ReviewUpdate


class ReviewService:
    """Business logic for review and testimonial operations."""

    def create_review(
        self, session: Session, *, user_id: uuid.UUID, review_in: ReviewCreate
    ) -> Review:
        """Create a new review."""
        db_obj = Review(user_id=user_id, **review_in.model_dump())
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj

    def get_reviews_for_coach(
        self,
        session: Session,
        *,
        coach_profile_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        featured_only: bool = False,
        min_rating: Optional[int] = None,
    ) -> list[Review]:
        """Get reviews for a specific coach."""
        statement = select(Review).where(
            and_(
                Review.coach_profile_id == coach_profile_id,
                Review.is_public == True
            )
        )

        if featured_only:
            statement = statement.where(Review.is_featured == True)

        if min_rating:
            statement = statement.where(Review.rating >= min_rating)

        statement = statement.order_by(Review.created_at.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()

    def count_reviews_for_coach(
        self,
        session: Session,
        *,
        coach_profile_id: uuid.UUID,
        featured_only: bool = False,
        min_rating: Optional[int] = None,
    ) -> int:
        """Count reviews for a specific coach."""
        statement = select(func.count(Review.id)).where(
            and_(
                Review.coach_profile_id == coach_profile_id,
                Review.is_public == True
            )
        )

        if featured_only:
            statement = statement.where(Review.is_featured == True)

        if min_rating:
            statement = statement.where(Review.rating >= min_rating)

        return session.exec(statement).one()

    def get_testimonials_for_coach(
        self,
        session: Session,
        *,
        coach_profile_id: uuid.UUID,
        skip: int = 0,
        limit: int = 10,
        featured_only: bool = True,
        min_rating: int = 4,
    ) -> list[Review]:
        """Get testimonials (high-rated reviews) for a specific coach."""
        return self.get_reviews_for_coach(
            session=session,
            coach_profile_id=coach_profile_id,
            skip=skip,
            limit=limit,
            featured_only=featured_only,
            min_rating=min_rating
        )

    def count_testimonials_for_coach(
        self,
        session: Session,
        *,
        coach_profile_id: uuid.UUID,
        featured_only: bool = True,
        min_rating: int = 4,
    ) -> int:
        """Count testimonials for a specific coach."""
        return self.count_reviews_for_coach(
            session=session,
            coach_profile_id=coach_profile_id,
            featured_only=featured_only,
            min_rating=min_rating
        )

    def get_featured_testimonials(
        self,
        session: Session,
        *,
        limit: int = 6,
    ) -> list[Review]:
        """Get featured testimonials across all coaches."""
        statement = (
            select(Review)
            .where(
                and_(
                    Review.is_featured == True,
                    Review.is_public == True,
                    Review.rating >= 4
                )
            )
            .order_by(Review.created_at.desc())
            .limit(limit)
        )
        return session.exec(statement).all()

    def count_all_testimonials(self, session: Session) -> int:
        """Count all testimonials across the platform."""
        statement = select(func.count(Review.id)).where(
            and_(
                Review.is_featured == True,
                Review.is_public == True,
                Review.rating >= 4
            )
        )
        return session.exec(statement).one()

    def get_overall_average_rating(self, session: Session) -> Optional[float]:
        """Get overall average rating across all reviews."""
        result = session.exec(
            select(func.avg(Review.rating)).where(Review.is_public == True)
        ).first()

        return round(result, 2) if result else None

    def feature_review(
        self,
        session: Session,
        *,
        review_id: uuid.UUID,
        coach_profile_id: uuid.UUID,
    ) -> Optional[Review]:
        """Feature a review as a testimonial."""
        review = session.exec(
            select(Review).where(
                and_(
                    Review.id == review_id,
                    Review.coach_profile_id == coach_profile_id
                )
            )
        ).first()

        if review:
            review.is_featured = True
            session.commit()
            session.refresh(review)

        return review

    def update_review(
        self,
        session: Session,
        *,
        review_id: uuid.UUID,
        review_update: ReviewUpdate,
    ) -> Optional[Review]:
        """Update a review."""
        review = session.get(Review, review_id)
        if review:
            update_data = review_update.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(review, field, value)
            session.commit()
            session.refresh(review)
        return review
