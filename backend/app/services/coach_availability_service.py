import uuid
from datetime import datetime, timedelta, time, date
from typing import List, Dict

from fastapi import HTTPException
from sqlmodel import Session

from app.models.coach_availability import AvailabilityType, CoachAvailability, DayOfWeek
from app.repositories.coach_availability_repository import CoachAvailabilityRepository
from app.repositories.coach_repository import CoachRepository
from app.schemas.coach_availability import (
    AvailableTimeSlot,
    AvailableTimeSlotsResponse,
    CoachAvailabilityCreate,
    CoachAvailabilityUpdate,
    WeeklyAvailabilityResponse,
    BulkAvailabilityCreate,
    BulkAvailabilityResponse
)


class CoachAvailabilityService:
    """Business logic for coach availability management."""
    
    def __init__(self):
        self.availability_repo = CoachAvailabilityRepository()
        self.coach_repo = CoachRepository()
    
    def create_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        availability_data: CoachAvailabilityCreate
    ) -> CoachAvailability:
        """Create a new availability slot for a coach."""
        # Verify coach exists
        coach = self.coach_repo.get(session, coach_id)
        if not coach:
            raise HTTPException(status_code=404, detail="Coach not found")
        
        # Check for conflicts
        existing = self.availability_repo.get_coach_availability(
            session,
            coach_id=coach_id,
            day_of_week=DayOfWeek(availability_data.day_of_week),
            availability_type=AvailabilityType(availability_data.availability_type)
        )
        
        # Check for time overlaps
        has_conflict = any(
            self._times_overlap(
                availability_data.start_time,
                availability_data.end_time,
                existing_slot.start_time,
                existing_slot.end_time
            )
            for existing_slot in existing
        )
        
        if has_conflict:
            raise HTTPException(
                status_code=409,
                detail="Time slot conflicts with existing availability"
            )
        
        # Create the availability
        availability_dict = availability_data.model_dump()
        availability_dict["coach_id"] = coach_id
        
        db_availability = CoachAvailability(**availability_dict)
        session.add(db_availability)
        session.commit()
        session.refresh(db_availability)
        
        return db_availability
    
    def get_coach_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        day_of_week: DayOfWeek = None,
        availability_type: AvailabilityType = None
    ) -> List[CoachAvailability]:
        """Get availability slots for a coach."""
        return self.availability_repo.get_coach_availability(
            session,
            coach_id=coach_id,
            day_of_week=day_of_week,
            availability_type=availability_type
        )
    
    def get_available_time_slots(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        target_date: datetime,
        duration_minutes: int = 60,
        timezone: str = "UTC"
    ) -> AvailableTimeSlotsResponse:
        """Get available time slots for a coach on a specific date."""
        # Get coach availability for the date
        availability_slots = self.availability_repo.get_availability_for_date(
            session,
            coach_id=coach_id,
            target_date=target_date
        )
        
        if not availability_slots:
            return AvailableTimeSlotsResponse(
                date=target_date,
                coach_id=coach_id,
                timezone=timezone,
                slots=[],
                total_slots=0
            )
        
        # Get existing bookings for the date
        existing_bookings = self.availability_repo.get_existing_bookings_for_date(
            session,
            coach_id=coach_id,
            target_date=target_date
        )
        
        # Generate available time slots
        available_slots = []
        
        for availability in availability_slots:
            # Generate time slots for this availability window
            slots = self._generate_time_slots(
                target_date.date(),
                availability.start_time,
                availability.end_time,
                duration_minutes,
                timezone
            )
            
            # Filter out slots that conflict with existing bookings
            for slot in slots:
                if not self._slot_conflicts_with_bookings(slot, existing_bookings):
                    available_slots.append(slot)
        
        # Sort slots by start time
        available_slots.sort(key=lambda x: x.start_time)
        
        return AvailableTimeSlotsResponse(
            date=target_date,
            coach_id=coach_id,
            timezone=timezone,
            slots=available_slots,
            total_slots=len(available_slots)
        )
    
    def get_weekly_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        start_date: datetime,
        duration_minutes: int = 60,
        timezone: str = "UTC"
    ) -> WeeklyAvailabilityResponse:
        """Get available time slots for a coach for a full week."""
        end_date = start_date + timedelta(days=7)
        daily_availability = {}
        total_slots = 0
        
        # Get availability for each day of the week
        current_date = start_date
        while current_date < end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            
            day_slots = self.get_available_time_slots(
                session,
                coach_id=coach_id,
                target_date=current_date,
                duration_minutes=duration_minutes,
                timezone=timezone
            )
            
            daily_availability[date_str] = day_slots.slots
            total_slots += day_slots.total_slots
            
            current_date += timedelta(days=1)
        
        return WeeklyAvailabilityResponse(
            coach_id=coach_id,
            start_date=start_date,
            end_date=end_date,
            timezone=timezone,
            daily_availability=daily_availability,
            total_slots=total_slots
        )
    
    def create_bulk_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        bulk_data: BulkAvailabilityCreate
    ) -> BulkAvailabilityResponse:
        """Create multiple availability slots in bulk."""
        # Verify coach exists
        coach = self.coach_repo.get(session, coach_id)
        if not coach:
            raise HTTPException(status_code=404, detail="Coach not found")
        
        created, errors = self.availability_repo.create_bulk_availability(
            session,
            coach_id=coach_id,
            availabilities=bulk_data.availabilities
        )
        
        return BulkAvailabilityResponse(
            created=created,
            errors=errors,
            total_created=len(created),
            total_errors=len(errors)
        )
    
    def update_availability(
        self,
        session: Session,
        *,
        availability_id: uuid.UUID,
        coach_id: uuid.UUID,
        availability_data: CoachAvailabilityUpdate
    ) -> CoachAvailability:
        """Update an availability slot."""
        availability = self.availability_repo.get(session, availability_id)
        if not availability:
            raise HTTPException(status_code=404, detail="Availability slot not found")
        
        if availability.coach_id != coach_id:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to update this availability slot"
            )
        
        updated_availability = self.availability_repo.update(
            session,
            db_obj=availability,
            obj_in=availability_data
        )
        
        return updated_availability
    
    def delete_availability(
        self,
        session: Session,
        *,
        availability_id: uuid.UUID,
        coach_id: uuid.UUID
    ) -> bool:
        """Delete an availability slot."""
        availability = self.availability_repo.get(session, availability_id)
        if not availability:
            raise HTTPException(status_code=404, detail="Availability slot not found")
        
        if availability.coach_id != coach_id:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to delete this availability slot"
            )
        
        session.delete(availability)
        session.commit()
        return True
    
    def _generate_time_slots(
        self,
        target_date: date,
        start_time: time,
        end_time: time,
        duration_minutes: int,
        timezone: str
    ) -> List[AvailableTimeSlot]:
        """Generate time slots within an availability window."""
        slots = []
        
        # Convert to datetime objects
        current_datetime = datetime.combine(target_date, start_time)
        end_datetime = datetime.combine(target_date, end_time)
        
        # Generate slots
        while current_datetime + timedelta(minutes=duration_minutes) <= end_datetime:
            slot_end = current_datetime + timedelta(minutes=duration_minutes)
            
            slots.append(AvailableTimeSlot(
                start_time=current_datetime,
                end_time=slot_end,
                duration_minutes=duration_minutes,
                is_available=True,
                timezone=timezone
            ))
            
            # Move to next slot (typically 15-30 minute intervals)
            current_datetime += timedelta(minutes=15)  # 15-minute intervals
        
        return slots
    
    def _slot_conflicts_with_bookings(
        self,
        slot: AvailableTimeSlot,
        bookings: List
    ) -> bool:
        """Check if a time slot conflicts with existing bookings."""
        for booking in bookings:
            booking_end = booking.session_time + timedelta(minutes=booking.duration)
            
            if (slot.start_time < booking_end and slot.end_time > booking.session_time):
                return True
        
        return False
    
    @staticmethod
    def _times_overlap(start1, end1, start2, end2) -> bool:
        """Check if two time ranges overlap."""
        return start1 < end2 and end1 > start2
