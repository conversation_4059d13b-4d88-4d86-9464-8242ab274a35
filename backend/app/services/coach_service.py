import uuid

from sqlmodel import Session

from app.models.coach import Coach<PERSON><PERSON><PERSON><PERSON>
from app.repositories.coach_repository import CoachRepository
from app.schemas.coach import CoachProfileCreate, CoachProfileUpdate, CoachSearchFilters


class CoachService:
    """Business logic for coach-related operations."""

    def __init__(self):
        self.coach_repo = CoachRepository()

    def search_coaches(
        self,
        session: Session,
        *,
        filters: CoachSearchFilters,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[list[CoachProfile], int]:
        """Search coaches with filters and return results with total count."""
        coaches = self.coach_repo.search_with_filters(
            session, filters=filters, skip=skip, limit=limit
        )
        total_count = self.coach_repo.count_with_filters(session, filters=filters)

        return coaches, total_count

    def get_coach_by_id(
        self, session: Session, *, coach_id: uuid.UUID
    ) -> CoachProfile | None:
        """Get coach profile by ID."""
        return self.coach_repo.get(session, coach_id)

    def get_coach_by_user_id(
        self, session: Session, *, user_id: uuid.UUID
    ) -> CoachProfile | None:
        """Get coach profile by user ID."""
        return self.coach_repo.get_by_user_id(session, user_id=user_id)

    def create_coach_profile(
        self, session: Session, *, user_id: uuid.UUID, coach_data: CoachProfileCreate
    ) -> CoachProfile:
        """Create a new coach profile."""
        # Create coach profile
        coach_profile_data = coach_data.model_dump()
        coach_profile_data["user_id"] = user_id

        db_coach = CoachProfile(**coach_profile_data)
        session.add(db_coach)
        session.commit()
        session.refresh(db_coach)

        return db_coach

    def update_coach_profile(
        self,
        session: Session,
        *,
        coach_profile: CoachProfile,
        coach_data: CoachProfileUpdate,
    ) -> CoachProfile:
        """Update an existing coach profile."""
        return self.coach_repo.update(session, db_obj=coach_profile, obj_in=coach_data)
