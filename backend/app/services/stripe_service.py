import json
import uuid
from datetime import datetime
from typing import Optional

import stripe
from fastapi import HTT<PERSON><PERSON>xception
from sqlmodel import Session

from app.core.config import settings
from app.models.booking import Booking, BookingStatus
from app.models.payment import Payment, PaymentStatus, StripeWebhookEvent
from app.repositories.booking_repository import BookingRepository
from app.schemas.payment import StripeCheckoutRequest, StripeCheckoutResponse

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeService:
    """Service for handling Stripe payment operations."""

    def __init__(self):
        self.booking_repo = BookingRepository()

    def create_checkout_session(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        checkout_request: StripeCheckoutRequest
    ) -> StripeCheckoutResponse:
        """Create a Stripe checkout session for a booking."""
        # Get the booking
        booking = self.booking_repo.get(session, checkout_request.booking_id)
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")

        # Verify user owns the booking
        if booking.user_id != user_id:
            raise HTTPException(status_code=403, detail="Not authorized to pay for this booking")

        # Check if booking is in correct status
        if booking.status != BookingStatus.PENDING.value:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot pay for booking with status: {booking.status}"
            )

        # Check if payment already exists
        existing_payment = session.query(Payment).filter(Payment.booking_id == booking.id).first()
        if existing_payment and existing_payment.status == PaymentStatus.SUCCEEDED.value:
            raise HTTPException(status_code=400, detail="Booking is already paid")

        # Get coach information for pricing
        coach = booking.coach
        if not coach or not coach.hourly_rate:
            raise HTTPException(status_code=400, detail="Coach hourly rate not set")

        # Calculate total amount (hourly rate * duration in hours)
        duration_hours = booking.duration / 60.0
        total_amount = coach.hourly_rate * duration_hours

        # Create or update payment record
        if existing_payment:
            payment = existing_payment
            payment.amount = total_amount
            payment.status = PaymentStatus.PENDING.value
        else:
            payment = Payment(
                booking_id=booking.id,
                user_id=user_id,
                amount=total_amount,
                currency="usd",
                status=PaymentStatus.PENDING.value,
                description=f"Spiritual coaching session with {coach.user.full_name}"
            )
            session.add(payment)

        session.commit()
        session.refresh(payment)

        try:
            # Create Stripe checkout session
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': f'Spiritual Coaching Session',
                            'description': f'{booking.duration} minute session with {coach.user.full_name}',
                            'metadata': {
                                'coach_name': coach.user.full_name,
                                'session_duration': str(booking.duration),
                                'session_type': booking.session_type or 'General Coaching'
                            }
                        },
                        'unit_amount': int(total_amount * 100),  # Stripe expects cents
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=checkout_request.success_url or settings.STRIPE_SUCCESS_URL,
                cancel_url=checkout_request.cancel_url or settings.STRIPE_CANCEL_URL,
                metadata={
                    'booking_id': str(booking.id),
                    'payment_id': str(payment.id),
                    'user_id': str(user_id),
                    'coach_id': str(coach.id)
                },
                customer_email=booking.user.email,
                expires_at=int((payment.created_at.timestamp() + 3600))  # 1 hour expiry
            )

            # Update payment with Stripe session ID
            payment.stripe_checkout_session_id = checkout_session.id
            payment.status = PaymentStatus.PROCESSING.value
            session.commit()

            return StripeCheckoutResponse(
                checkout_url=checkout_session.url,
                session_id=checkout_session.id,
                payment_id=payment.id
            )

        except stripe.error.StripeError as e:
            # Handle Stripe errors
            payment.status = PaymentStatus.FAILED.value
            payment.failure_reason = str(e)
            session.commit()
            raise HTTPException(status_code=400, detail=f"Stripe error: {str(e)}")

    def handle_webhook_event(
        self,
        session: Session,
        *,
        payload: str,
        sig_header: str
    ) -> dict:
        """Handle Stripe webhook events."""
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError:
            raise HTTPException(status_code=400, detail="Invalid signature")

        # Check if we've already processed this event
        existing_event = session.query(StripeWebhookEvent).filter(
            StripeWebhookEvent.stripe_event_id == event['id']
        ).first()

        if existing_event and existing_event.processed:
            return {"status": "already_processed"}

        # Create or update webhook event record
        if not existing_event:
            webhook_event = StripeWebhookEvent(
                stripe_event_id=event['id'],
                event_type=event['type'],
                event_data=json.dumps(event['data'])
            )
            session.add(webhook_event)
        else:
            webhook_event = existing_event

        try:
            # Handle different event types
            if event['type'] == 'checkout.session.completed':
                self._handle_checkout_completed(session, event)
            elif event['type'] == 'payment_intent.succeeded':
                self._handle_payment_succeeded(session, event)
            elif event['type'] == 'payment_intent.payment_failed':
                self._handle_payment_failed(session, event)

            # Mark event as processed
            webhook_event.processed = True
            webhook_event.processed_at = datetime.utcnow()
            session.commit()

            return {"status": "processed", "event_type": event['type']}

        except Exception as e:
            # Log error and mark as failed
            webhook_event.processing_error = str(e)
            session.commit()
            raise HTTPException(status_code=500, detail=f"Webhook processing error: {str(e)}")

    def _handle_checkout_completed(self, session: Session, event: dict) -> None:
        """Handle successful checkout completion."""
        checkout_session = event['data']['object']
        session_id = checkout_session['id']

        # Find payment by checkout session ID
        payment = session.query(Payment).filter(
            Payment.stripe_checkout_session_id == session_id
        ).first()

        if not payment:
            raise Exception(f"Payment not found for checkout session: {session_id}")

        # Update payment status
        payment.status = PaymentStatus.SUCCEEDED.value
        payment.paid_at = datetime.utcnow()
        payment.stripe_payment_intent_id = checkout_session.get('payment_intent')

        # Update booking status to confirmed
        booking = payment.booking
        booking.status = BookingStatus.CONFIRMED.value
        booking.updated_at = datetime.utcnow()

        session.commit()

    def _handle_payment_succeeded(self, session: Session, event: dict) -> None:
        """Handle successful payment intent."""
        payment_intent = event['data']['object']
        payment_intent_id = payment_intent['id']

        # Find payment by payment intent ID
        payment = session.query(Payment).filter(
            Payment.stripe_payment_intent_id == payment_intent_id
        ).first()

        if payment:
            payment.status = PaymentStatus.SUCCEEDED.value
            payment.paid_at = datetime.utcnow()

            # Update booking status
            booking = payment.booking
            booking.status = BookingStatus.CONFIRMED.value
            booking.updated_at = datetime.utcnow()

            session.commit()

    def _handle_payment_failed(self, session: Session, event: dict) -> None:
        """Handle failed payment intent."""
        payment_intent = event['data']['object']
        payment_intent_id = payment_intent['id']

        # Find payment by payment intent ID
        payment = session.query(Payment).filter(
            Payment.stripe_payment_intent_id == payment_intent_id
        ).first()

        if payment:
            payment.status = PaymentStatus.FAILED.value
            payment.failure_reason = payment_intent.get('last_payment_error', {}).get('message', 'Payment failed')
            session.commit()

    def create_refund(
        self,
        session: Session,
        *,
        payment_id: uuid.UUID,
        amount: Optional[float] = None,
        reason: str
    ) -> dict:
        """Create a refund for a payment."""
        payment = session.query(Payment).filter(Payment.id == payment_id).first()
        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")

        if payment.status != PaymentStatus.SUCCEEDED.value:
            raise HTTPException(status_code=400, detail="Can only refund successful payments")

        if not payment.stripe_payment_intent_id:
            raise HTTPException(status_code=400, detail="No Stripe payment intent found")

        try:
            # Create refund in Stripe
            refund_amount = amount or payment.amount
            stripe_refund = stripe.Refund.create(
                payment_intent=payment.stripe_payment_intent_id,
                amount=int(refund_amount * 100),  # Convert to cents
                reason='requested_by_customer',
                metadata={
                    'payment_id': str(payment.id),
                    'booking_id': str(payment.booking_id),
                    'refund_reason': reason
                }
            )

            # Update payment record
            payment.status = PaymentStatus.REFUNDED.value
            payment.refund_amount = refund_amount
            payment.refund_reason = reason
            payment.updated_at = datetime.utcnow()

            # Update booking status
            booking = payment.booking
            booking.status = BookingStatus.CANCELLED.value
            booking.updated_at = datetime.utcnow()

            session.commit()

            return {
                "refund_id": stripe_refund.id,
                "amount": refund_amount,
                "status": stripe_refund.status
            }

        except stripe.error.StripeError as e:
            raise HTTPException(status_code=400, detail=f"Refund failed: {str(e)}")
