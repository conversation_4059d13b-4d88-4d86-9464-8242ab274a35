import uuid
from datetime import datetime
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlmodel import Session

from app.models.event import Event, EventRSVP, EventStatus, RSVPStatus
from app.repositories.coach_repository import CoachRepository
from app.repositories.event_repository import EventRepository, EventRSVPRepository
from app.schemas.event import (
    EventCreate,
    EventSearchFilters,
    EventUpdate,
    RSVPCreate,
)


class EventService:
    """Business logic for event operations."""
    
    def __init__(self):
        self.event_repo = EventRepository()
        self.rsvp_repo = EventRSVPRepository()
        self.coach_repo = CoachRepository()
    
    def create_event(
        self,
        session: Session,
        *,
        host_id: uuid.UUID,
        event_in: EventCreate
    ) -> Event:
        """Create a new event."""
        # Verify the host is a coach
        coach = self.coach_repo.get_by_user_id(session, host_id)
        if not coach:
            raise HTTPException(status_code=403, detail="Only coaches can create events")
        
        # Create event data
        event_data = event_in.model_dump()
        event_data["host_id"] = coach.id
        
        event = Event(**event_data)
        session.add(event)
        session.commit()
        session.refresh(event)
        
        return event
    
    def update_event(
        self,
        session: Session,
        *,
        event_id: uuid.UUID,
        host_id: uuid.UUID,
        event_update: EventUpdate
    ) -> Optional[Event]:
        """Update an event (only by host)."""
        event = session.get(Event, event_id)
        if not event:
            return None
        
        # Verify the user is the host
        coach = self.coach_repo.get_by_user_id(session, host_id)
        if not coach or event.host_id != coach.id:
            raise HTTPException(status_code=403, detail="Only the event host can update this event")
        
        # Update event
        update_data = event_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(event, field, value)
        
        event.updated_at = datetime.utcnow()
        session.commit()
        session.refresh(event)
        
        return event
    
    def delete_event(
        self,
        session: Session,
        *,
        event_id: uuid.UUID,
        host_id: uuid.UUID
    ) -> bool:
        """Delete an event (only by host)."""
        event = session.get(Event, event_id)
        if not event:
            return False
        
        # Verify the user is the host
        coach = self.coach_repo.get_by_user_id(session, host_id)
        if not coach or event.host_id != coach.id:
            raise HTTPException(status_code=403, detail="Only the event host can delete this event")
        
        # Check if event has RSVPs
        rsvps = self.rsvp_repo.get_by_event(session, event_id=event_id, limit=1)
        if rsvps:
            # Don't delete, just cancel
            event.status = EventStatus.CANCELLED.value
            session.commit()
        else:
            # Safe to delete
            session.delete(event)
            session.commit()
        
        return True
    
    def search_events(
        self,
        session: Session,
        *,
        filters: EventSearchFilters,
        skip: int = 0,
        limit: int = 100
    ) -> tuple[list[Event], int]:
        """Search events with filters."""
        return self.event_repo.search_events(
            session=session,
            filters=filters,
            skip=skip,
            limit=limit
        )
    
    def get_event_by_id(
        self,
        session: Session,
        *,
        event_id: uuid.UUID
    ) -> Optional[Event]:
        """Get event by ID."""
        return session.get(Event, event_id)
    
    def get_events_by_host(
        self,
        session: Session,
        *,
        host_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[EventStatus] = None
    ) -> list[Event]:
        """Get events by host."""
        coach = self.coach_repo.get_by_user_id(session, host_id)
        if not coach:
            return []
        
        return self.event_repo.get_by_host(
            session=session,
            host_id=coach.id,
            skip=skip,
            limit=limit,
            status=status
        )
    
    def get_upcoming_events(
        self,
        session: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        host_id: Optional[uuid.UUID] = None
    ) -> list[Event]:
        """Get upcoming events."""
        coach_profile_id = None
        if host_id:
            coach = self.coach_repo.get_by_user_id(session, host_id)
            if coach:
                coach_profile_id = coach.id
        
        return self.event_repo.get_upcoming_events(
            session=session,
            skip=skip,
            limit=limit,
            host_id=coach_profile_id
        )
    
    def create_rsvp(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        rsvp_in: RSVPCreate
    ) -> EventRSVP:
        """Create an RSVP for an event."""
        # Check if user already has an RSVP for this event
        existing_rsvp = self.rsvp_repo.get_by_user_and_event(
            session=session,
            user_id=user_id,
            event_id=rsvp_in.event_id
        )
        
        if existing_rsvp:
            raise HTTPException(status_code=400, detail="You have already RSVP'd for this event")
        
        # Get the event
        event = session.get(Event, rsvp_in.event_id)
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        # Check if event is published and not in the past
        if event.status != EventStatus.PUBLISHED.value:
            raise HTTPException(status_code=400, detail="Event is not available for registration")
        
        if event.event_date <= datetime.utcnow():
            raise HTTPException(status_code=400, detail="Cannot RSVP for past events")
        
        # Check registration deadline
        if event.registration_deadline and datetime.utcnow() > event.registration_deadline:
            raise HTTPException(status_code=400, detail="Registration deadline has passed")
        
        # Check capacity and determine if waitlisted
        is_waitlisted = False
        waitlist_position = None
        
        if event.current_attendees >= event.capacity:
            if not event.allow_waitlist:
                raise HTTPException(status_code=400, detail="Event is full and waitlist is not allowed")
            
            is_waitlisted = True
            waitlist_position = self.rsvp_repo.get_next_waitlist_position(
                session=session,
                event_id=event.id
            )
        
        # Determine if payment is required
        payment_required = event.price > 0
        
        # Create RSVP
        rsvp_data = rsvp_in.model_dump()
        rsvp_data.update({
            "user_id": user_id,
            "payment_required": payment_required,
            "is_waitlisted": is_waitlisted,
            "waitlist_position": waitlist_position,
            "status": RSVPStatus.PENDING.value if payment_required or event.require_approval else RSVPStatus.CONFIRMED.value
        })
        
        rsvp = EventRSVP(**rsvp_data)
        session.add(rsvp)
        session.commit()
        session.refresh(rsvp)
        
        # Update event attendee count if confirmed and not waitlisted
        if rsvp.status == RSVPStatus.CONFIRMED.value and not is_waitlisted:
            self.event_repo.update_attendee_count(session=session, event_id=event.id)
        
        return rsvp
    
    def cancel_rsvp(
        self,
        session: Session,
        *,
        rsvp_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[EventRSVP]:
        """Cancel an RSVP."""
        rsvp = session.get(EventRSVP, rsvp_id)
        if not rsvp:
            return None
        
        # Verify user owns the RSVP
        if rsvp.user_id != user_id:
            raise HTTPException(status_code=403, detail="You can only cancel your own RSVP")
        
        # Update RSVP status
        rsvp.status = RSVPStatus.CANCELLED.value
        session.commit()
        
        # Update event attendee count
        event = session.get(Event, rsvp.event_id)
        if event:
            self.event_repo.update_attendee_count(session=session, event_id=event.id)
            
            # Promote someone from waitlist if there's space
            if not rsvp.is_waitlisted:
                promoted_rsvps = self.rsvp_repo.promote_from_waitlist(
                    session=session,
                    event_id=event.id,
                    spots_available=1
                )
                
                # Update attendee count again if someone was promoted
                if promoted_rsvps:
                    self.event_repo.update_attendee_count(session=session, event_id=event.id)
        
        return rsvp
    
    def get_user_rsvps(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[EventRSVP]:
        """Get RSVPs for a user."""
        return self.rsvp_repo.get_by_user(
            session=session,
            user_id=user_id,
            skip=skip,
            limit=limit
        )
    
    def get_event_rsvps(
        self,
        session: Session,
        *,
        event_id: uuid.UUID,
        host_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[RSVPStatus] = None
    ) -> list[EventRSVP]:
        """Get RSVPs for an event (only by host)."""
        event = session.get(Event, event_id)
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        # Verify the user is the host
        coach = self.coach_repo.get_by_user_id(session, host_id)
        if not coach or event.host_id != coach.id:
            raise HTTPException(status_code=403, detail="Only the event host can view RSVPs")
        
        return self.rsvp_repo.get_by_event(
            session=session,
            event_id=event_id,
            status=status,
            skip=skip,
            limit=limit
        )
