import uuid
from datetime import datetime, timedelta
from typing import Optional

from sqlmodel import Session, and_, func, select

from app.models.booking import Booking, BookingStatus
from app.models.coach import CoachProfile
from app.models.payment import Payment, PaymentStatus
from app.models.review import Review
from app.models.user import User, UserRole
from app.repositories.booking_repository import BookingRepository
from app.repositories.coach_repository import CoachRepository
from app.repositories.payment_repository import PaymentRepository
from app.schemas.dashboard import (
    AdminDashboard,
    AdminDashboardSummary,
    CoachDashboard,
    CoachDashboardStats,
    DashboardBookingWithCoach,
    DashboardBookingWithUser,
    DashboardPayment,
    DashboardResponse,
    DashboardReview,
    DashboardReviewWithCoach,
    DashboardStats,
    TopRatedCoach,
    UserDashboard,
)
from app.services.review_service import ReviewService


class DashboardService:
    """Service for generating role-specific dashboard data."""

    def __init__(self):
        self.booking_repo = BookingRepository()
        self.coach_repo = CoachRepository()
        self.payment_repo = PaymentRepository()
        self.review_service = ReviewService()

    def get_dashboard_for_user(
        self,
        session: Session,
        *,
        user: User
    ) -> DashboardResponse:
        """Get dashboard data based on user role."""
        if user.role == UserRole.ADMIN.value:
            admin_dashboard = self._get_admin_dashboard(session)
            return DashboardResponse(
                user_role=user.role,
                admin_dashboard=admin_dashboard
            )
        elif user.role == UserRole.COACH.value:
            coach_dashboard = self._get_coach_dashboard(session, user_id=user.id)
            return DashboardResponse(
                user_role=user.role,
                coach_dashboard=coach_dashboard
            )
        else:  # Regular user
            user_dashboard = self._get_user_dashboard(session, user_id=user.id)
            return DashboardResponse(
                user_role=user.role,
                user_dashboard=user_dashboard
            )

    def _get_user_dashboard(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> UserDashboard:
        """Generate dashboard data for regular users."""
        user = session.get(User, user_id)
        if not user:
            raise ValueError("User not found")

        # Date ranges
        now = datetime.utcnow()
        thirty_days_ago = now - timedelta(days=30)
        thirty_days_ahead = now + timedelta(days=30)

        # Get upcoming sessions
        upcoming_sessions = self._get_user_upcoming_sessions(
            session, user_id=user_id, start_date=now, end_date=thirty_days_ahead
        )

        # Get recent bookings
        recent_bookings = self._get_user_recent_bookings(
            session, user_id=user_id, start_date=thirty_days_ago
        )

        # Get reviews left by user
        reviews_left = self._get_user_reviews(session, user_id=user_id)

        # Get recent payments
        recent_payments = self._get_user_recent_payments(
            session, user_id=user_id, start_date=thirty_days_ago
        )

        # Get statistics
        stats = self._get_user_stats(session, user_id=user_id)

        # Get favorite coaches (coaches user has booked most)
        favorite_coaches = self._get_user_favorite_coaches(session, user_id=user_id)

        # Get pending payments
        pending_payments = self._get_user_pending_payments(session, user_id=user_id)

        return UserDashboard(
            user=user,
            upcoming_sessions=upcoming_sessions,
            recent_bookings=recent_bookings,
            reviews_left=reviews_left,
            recent_payments=recent_payments,
            stats=stats,
            favorite_coaches=favorite_coaches,
            pending_payments=pending_payments
        )

    def _get_coach_dashboard(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> CoachDashboard:
        """Generate dashboard data for coaches."""
        user = session.get(User, user_id)
        if not user:
            raise ValueError("User not found")

        # Get coach profile
        coach_profile = self.coach_repo.get_by_user_id(session, user_id)
        if not coach_profile:
            raise ValueError("Coach profile not found")

        # Date ranges
        now = datetime.utcnow()
        thirty_days_ago = now - timedelta(days=30)
        thirty_days_ahead = now + timedelta(days=30)

        # Get upcoming sessions
        upcoming_sessions = self._get_coach_upcoming_sessions(
            session, coach_id=coach_profile.id, start_date=now, end_date=thirty_days_ahead
        )

        # Get recent sessions
        recent_sessions = self._get_coach_recent_sessions(
            session, coach_id=coach_profile.id, start_date=thirty_days_ago
        )

        # Get reviews received
        reviews_received = self._get_coach_reviews(session, coach_id=coach_profile.id)

        # Get recent earnings
        recent_earnings = self._get_coach_recent_earnings(
            session, coach_id=coach_profile.id, start_date=thirty_days_ago
        )

        # Get statistics
        stats = self._get_coach_stats(session, coach_id=coach_profile.id)

        # Get pending sessions
        pending_sessions = self._get_coach_pending_sessions(session, coach_id=coach_profile.id)

        # Get featured testimonials
        featured_testimonials = self._get_coach_featured_testimonials(session, coach_id=coach_profile.id)

        return CoachDashboard(
            user=user,
            coach_profile=coach_profile,
            upcoming_sessions=upcoming_sessions,
            recent_sessions=recent_sessions,
            reviews_received=reviews_received,
            recent_earnings=recent_earnings,
            stats=stats,
            pending_sessions=pending_sessions,
            featured_testimonials=featured_testimonials
        )

    def _get_admin_dashboard(self, session: Session) -> AdminDashboard:
        """Generate dashboard data for admins."""
        # Get platform-wide statistics
        total_users = session.exec(select(func.count(User.id))).one()
        total_coaches = session.exec(
            select(func.count(CoachProfile.id))
        ).one()
        total_bookings = session.exec(select(func.count(Booking.id))).one()
        total_payments = session.exec(
            select(func.sum(Payment.amount)).where(Payment.status == PaymentStatus.SUCCEEDED.value)
        ).one() or 0.0
        total_reviews = session.exec(select(func.count(Review.id))).one()

        # This month statistics
        first_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        this_month_bookings = session.exec(
            select(func.count(Booking.id)).where(Booking.created_at >= first_of_month)
        ).one()

        this_month_revenue = session.exec(
            select(func.sum(Payment.amount)).where(
                and_(
                    Payment.status == PaymentStatus.SUCCEEDED.value,
                    Payment.created_at >= first_of_month
                )
            )
        ).one() or 0.0

        this_month_new_users = session.exec(
            select(func.count(User.id)).where(User.created_at >= first_of_month)
        ).one()

        this_month_new_coaches = session.exec(
            select(func.count(CoachProfile.id)).where(CoachProfile.created_at >= first_of_month)
        ).one()

        # Recent activity (last 10 items)
        recent_bookings = session.exec(
            select(Booking).order_by(Booking.created_at.desc()).limit(10)
        ).all()

        recent_payments = session.exec(
            select(Payment).order_by(Payment.created_at.desc()).limit(10)
        ).all()

        recent_reviews = session.exec(
            select(Review).order_by(Review.created_at.desc()).limit(10)
        ).all()

        # Get top rated coaches
        top_rated_coaches = self._get_top_rated_coaches(session, limit=5)

        return AdminDashboard(
            total_users=total_users,
            total_coaches=total_coaches,
            total_bookings=total_bookings,
            total_payments=total_payments,
            total_reviews=total_reviews,
            recent_bookings=recent_bookings,
            recent_payments=recent_payments,
            recent_reviews=recent_reviews,
            this_month_bookings=this_month_bookings,
            this_month_revenue=this_month_revenue,
            this_month_new_users=this_month_new_users,
            this_month_new_coaches=this_month_new_coaches,
            top_rated_coaches=top_rated_coaches
        )

    def _get_user_upcoming_sessions(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime
    ) -> list[DashboardBookingWithCoach]:
        """Get upcoming sessions for a user."""
        bookings = session.exec(
            select(Booking)
            .where(
                and_(
                    Booking.user_id == user_id,
                    Booking.session_time >= start_date,
                    Booking.session_time <= end_date,
                    Booking.status.in_([BookingStatus.CONFIRMED.value, BookingStatus.PENDING.value])
                )
            )
            .order_by(Booking.session_time.asc())
            .limit(10)
        ).all()

        return [
            DashboardBookingWithCoach(
                id=booking.id,
                session_time=booking.session_time,
                duration=booking.duration,
                status=BookingStatus(booking.status),
                session_type=booking.session_type,
                notes=booking.notes,
                meeting_link=booking.meeting_link,
                created_at=booking.created_at,
                coach=booking.coach
            )
            for booking in bookings
        ]

    def _get_user_recent_bookings(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        start_date: datetime
    ) -> list[DashboardBookingWithCoach]:
        """Get recent bookings for a user."""
        bookings = session.exec(
            select(Booking)
            .where(
                and_(
                    Booking.user_id == user_id,
                    Booking.created_at >= start_date
                )
            )
            .order_by(Booking.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardBookingWithCoach(
                id=booking.id,
                session_time=booking.session_time,
                duration=booking.duration,
                status=BookingStatus(booking.status),
                session_type=booking.session_type,
                notes=booking.notes,
                meeting_link=booking.meeting_link,
                created_at=booking.created_at,
                coach=booking.coach
            )
            for booking in bookings
        ]

    def _get_user_reviews(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> list[DashboardReviewWithCoach]:
        """Get reviews left by a user."""
        reviews = session.exec(
            select(Review)
            .where(Review.user_id == user_id)
            .order_by(Review.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardReviewWithCoach(
                id=review.id,
                rating=review.rating,
                comment=review.comment,
                is_featured=review.is_featured,
                session_type=review.session_type,
                created_at=review.created_at,
                coach=review.coach_profile
            )
            for review in reviews
        ]

    def _get_user_recent_payments(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        start_date: datetime
    ) -> list[DashboardPayment]:
        """Get recent payments for a user."""
        payments = session.exec(
            select(Payment)
            .where(
                and_(
                    Payment.user_id == user_id,
                    Payment.created_at >= start_date
                )
            )
            .order_by(Payment.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardPayment(
                id=payment.id,
                amount=payment.amount,
                status=PaymentStatus(payment.status),
                payment_method=payment.payment_method,
                created_at=payment.created_at,
                paid_at=payment.paid_at
            )
            for payment in payments
        ]

    def _get_user_stats(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> DashboardStats:
        """Get statistics for a user."""
        # Booking statistics
        total_bookings = session.exec(
            select(func.count(Booking.id)).where(Booking.user_id == user_id)
        ).one()

        confirmed_bookings = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.user_id == user_id,
                    Booking.status == BookingStatus.CONFIRMED.value
                )
            )
        ).one()

        completed_bookings = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.user_id == user_id,
                    Booking.status == BookingStatus.COMPLETED.value
                )
            )
        ).one()

        cancelled_bookings = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.user_id == user_id,
                    Booking.status == BookingStatus.CANCELLED.value
                )
            )
        ).one()

        # Payment statistics
        total_spent = session.exec(
            select(func.sum(Payment.amount)).where(
                and_(
                    Payment.user_id == user_id,
                    Payment.status == PaymentStatus.SUCCEEDED.value
                )
            )
        ).one() or 0.0

        # Review statistics
        total_reviews = session.exec(
            select(func.count(Review.id)).where(Review.user_id == user_id)
        ).one()

        average_rating_given = session.exec(
            select(func.avg(Review.rating)).where(Review.user_id == user_id)
        ).one()

        return DashboardStats(
            total_bookings=total_bookings,
            confirmed_bookings=confirmed_bookings,
            completed_bookings=completed_bookings,
            cancelled_bookings=cancelled_bookings,
            total_spent=total_spent,
            total_reviews=total_reviews,
            average_rating_given=round(average_rating_given, 2) if average_rating_given else None
        )

    def _get_user_favorite_coaches(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> list:
        """Get user's favorite coaches (most booked)."""
        # Get coaches user has booked most frequently
        coach_booking_counts = session.exec(
            select(Booking.coach_id, func.count(Booking.id).label('booking_count'))
            .where(Booking.user_id == user_id)
            .group_by(Booking.coach_id)
            .order_by(func.count(Booking.id).desc())
            .limit(5)
        ).all()

        favorite_coaches = []
        for coach_id, _ in coach_booking_counts:
            coach = session.get(CoachProfile, coach_id)
            if coach:
                favorite_coaches.append(coach)

        return favorite_coaches

    def _get_user_pending_payments(
        self,
        session: Session,
        *,
        user_id: uuid.UUID
    ) -> list[DashboardPayment]:
        """Get pending payments for a user."""
        payments = session.exec(
            select(Payment)
            .where(
                and_(
                    Payment.user_id == user_id,
                    Payment.status == PaymentStatus.PENDING.value
                )
            )
            .order_by(Payment.created_at.desc())
        ).all()

        return [
            DashboardPayment(
                id=payment.id,
                amount=payment.amount,
                status=PaymentStatus(payment.status),
                payment_method=payment.payment_method,
                created_at=payment.created_at,
                paid_at=payment.paid_at
            )
            for payment in payments
        ]

    # Coach dashboard helper methods
    def _get_coach_upcoming_sessions(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime
    ) -> list[DashboardBookingWithUser]:
        """Get upcoming sessions for a coach."""
        bookings = session.exec(
            select(Booking)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.session_time >= start_date,
                    Booking.session_time <= end_date,
                    Booking.status.in_([BookingStatus.CONFIRMED.value, BookingStatus.PENDING.value])
                )
            )
            .order_by(Booking.session_time.asc())
            .limit(10)
        ).all()

        return [
            DashboardBookingWithUser(
                id=booking.id,
                session_time=booking.session_time,
                duration=booking.duration,
                status=BookingStatus(booking.status),
                session_type=booking.session_type,
                notes=booking.notes,
                meeting_link=booking.meeting_link,
                created_at=booking.created_at,
                user=booking.user
            )
            for booking in bookings
        ]

    def _get_coach_recent_sessions(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        start_date: datetime
    ) -> list[DashboardBookingWithUser]:
        """Get recent sessions for a coach."""
        bookings = session.exec(
            select(Booking)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.created_at >= start_date
                )
            )
            .order_by(Booking.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardBookingWithUser(
                id=booking.id,
                session_time=booking.session_time,
                duration=booking.duration,
                status=BookingStatus(booking.status),
                session_type=booking.session_type,
                notes=booking.notes,
                meeting_link=booking.meeting_link,
                created_at=booking.created_at,
                user=booking.user
            )
            for booking in bookings
        ]

    def _get_coach_reviews(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID
    ) -> list[DashboardReview]:
        """Get reviews for a coach."""
        reviews = session.exec(
            select(Review)
            .where(Review.coach_profile_id == coach_id)
            .order_by(Review.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardReview(
                id=review.id,
                rating=review.rating,
                comment=review.comment,
                is_featured=review.is_featured,
                session_type=review.session_type,
                created_at=review.created_at
            )
            for review in reviews
        ]

    def _get_coach_recent_earnings(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        start_date: datetime
    ) -> list[DashboardPayment]:
        """Get recent earnings for a coach."""
        payments = session.exec(
            select(Payment)
            .join(Booking, Payment.booking_id == Booking.id)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Payment.status == PaymentStatus.SUCCEEDED.value,
                    Payment.created_at >= start_date
                )
            )
            .order_by(Payment.created_at.desc())
            .limit(10)
        ).all()

        return [
            DashboardPayment(
                id=payment.id,
                amount=payment.amount,
                status=PaymentStatus(payment.status),
                payment_method=payment.payment_method,
                created_at=payment.created_at,
                paid_at=payment.paid_at
            )
            for payment in payments
        ]

    def _get_coach_stats(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID
    ) -> CoachDashboardStats:
        """Get statistics for a coach."""
        # Session statistics
        total_sessions = session.exec(
            select(func.count(Booking.id)).where(Booking.coach_id == coach_id)
        ).one()

        confirmed_sessions = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.status == BookingStatus.CONFIRMED.value
                )
            )
        ).one()

        completed_sessions = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.status == BookingStatus.COMPLETED.value
                )
            )
        ).one()

        cancelled_sessions = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.status == BookingStatus.CANCELLED.value
                )
            )
        ).one()

        # Earnings statistics
        total_earnings = session.exec(
            select(func.sum(Payment.amount))
            .join(Booking, Payment.booking_id == Booking.id)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Payment.status == PaymentStatus.SUCCEEDED.value
                )
            )
        ).one() or 0.0

        # Review statistics
        total_reviews_received = session.exec(
            select(func.count(Review.id)).where(Review.coach_profile_id == coach_id)
        ).one()

        average_rating_received = session.exec(
            select(func.avg(Review.rating)).where(Review.coach_profile_id == coach_id)
        ).one()

        featured_testimonials = session.exec(
            select(func.count(Review.id)).where(
                and_(
                    Review.coach_profile_id == coach_id,
                    Review.is_featured == True
                )
            )
        ).one()

        # This month statistics
        first_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        this_month_sessions = session.exec(
            select(func.count(Booking.id)).where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.created_at >= first_of_month
                )
            )
        ).one()

        this_month_earnings = session.exec(
            select(func.sum(Payment.amount))
            .join(Booking, Payment.booking_id == Booking.id)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Payment.status == PaymentStatus.SUCCEEDED.value,
                    Payment.created_at >= first_of_month
                )
            )
        ).one() or 0.0

        return CoachDashboardStats(
            total_sessions=total_sessions,
            confirmed_sessions=confirmed_sessions,
            completed_sessions=completed_sessions,
            cancelled_sessions=cancelled_sessions,
            total_earnings=total_earnings,
            total_reviews_received=total_reviews_received,
            average_rating_received=round(average_rating_received, 2) if average_rating_received else None,
            featured_testimonials=featured_testimonials,
            this_month_sessions=this_month_sessions,
            this_month_earnings=this_month_earnings
        )

    def _get_coach_pending_sessions(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID
    ) -> list[DashboardBookingWithUser]:
        """Get pending sessions for a coach."""
        bookings = session.exec(
            select(Booking)
            .where(
                and_(
                    Booking.coach_id == coach_id,
                    Booking.status == BookingStatus.PENDING.value
                )
            )
            .order_by(Booking.session_time.asc())
            .limit(5)
        ).all()

        return [
            DashboardBookingWithUser(
                id=booking.id,
                session_time=booking.session_time,
                duration=booking.duration,
                status=BookingStatus(booking.status),
                session_type=booking.session_type,
                notes=booking.notes,
                meeting_link=booking.meeting_link,
                created_at=booking.created_at,
                user=booking.user
            )
            for booking in bookings
        ]

    def _get_coach_featured_testimonials(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID
    ) -> list[DashboardReview]:
        """Get featured testimonials for a coach."""
        reviews = session.exec(
            select(Review)
            .where(
                and_(
                    Review.coach_profile_id == coach_id,
                    Review.is_featured == True
                )
            )
            .order_by(Review.created_at.desc())
            .limit(5)
        ).all()

        return [
            DashboardReview(
                id=review.id,
                rating=review.rating,
                comment=review.comment,
                is_featured=review.is_featured,
                session_type=review.session_type,
                created_at=review.created_at
            )
            for review in reviews
        ]

    def _get_top_rated_coaches(
        self,
        session: Session,
        *,
        limit: int = 5
    ) -> list[TopRatedCoach]:
        """Get top rated coaches with their statistics."""
        # Query to get coaches with their average ratings and review counts
        # Only include coaches with at least 3 reviews to ensure meaningful ratings
        coach_stats = session.exec(
            select(
                CoachProfile.id,
                CoachProfile.first_name,
                CoachProfile.last_name,
                CoachProfile.profile_image_url,
                func.avg(Review.rating).label('average_rating'),
                func.count(Review.id).label('total_reviews')
            )
            .join(Review, CoachProfile.id == Review.coach_profile_id)
            .group_by(CoachProfile.id, CoachProfile.first_name, CoachProfile.last_name, CoachProfile.profile_image_url)
            .having(func.count(Review.id) >= 3)  # At least 3 reviews
            .order_by(func.avg(Review.rating).desc(), func.count(Review.id).desc())
            .limit(limit)
        ).all()

        top_coaches = []
        for coach_stat in coach_stats:
            # Get total sessions for this coach
            total_sessions = session.exec(
                select(func.count(Booking.id)).where(Booking.coach_id == coach_stat.id)
            ).one()

            # Create full name
            full_name = f"{coach_stat.first_name} {coach_stat.last_name}".strip()

            top_coaches.append(TopRatedCoach(
                id=coach_stat.id,
                name=full_name,
                average_rating=round(coach_stat.average_rating, 2),
                total_reviews=coach_stat.total_reviews,
                total_sessions=total_sessions,
                profile_image_url=coach_stat.profile_image_url
            ))

        return top_coaches

    def get_admin_dashboard_summary(
        self,
        session: Session
    ) -> AdminDashboardSummary:
        """Get simplified admin dashboard summary with core metrics."""
        # Get basic counts
        total_users = session.exec(select(func.count(User.id))).one()
        total_coaches = session.exec(select(func.count(CoachProfile.id))).one()

        # This month bookings
        first_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_bookings = session.exec(
            select(func.count(Booking.id)).where(Booking.created_at >= first_of_month)
        ).one()

        # Top rated coaches
        top_rated_coaches = self._get_top_rated_coaches(session, limit=5)

        return AdminDashboardSummary(
            total_users=total_users,
            total_coaches=total_coaches,
            this_month_bookings=this_month_bookings,
            top_rated_coaches=top_rated_coaches
        )