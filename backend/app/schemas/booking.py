import uuid
from datetime import datetime
from typing import Optional

from pydantic import Field, validator
from sqlmodel import SQLModel

from app.models.booking import BookingStatus
from app.schemas.common import PaginatedResponse
from app.schemas.coach import CoachProfilePublic
from app.schemas.user import UserPublic


class BookingBase(SQLModel):
    session_time: datetime = Field(description="Scheduled session date and time")
    duration: int = Field(ge=15, le=480, default=60, description="Session duration in minutes")
    notes: Optional[str] = Field(default=None, max_length=1000, description="Additional notes for the session")
    session_type: Optional[str] = Field(default=None, max_length=100, description="Type of session (e.g., Energy Healing)")


class BookingCreate(BookingBase):
    coach_id: uuid.UUID = Field(description="ID of the coach to book")
    
    @validator('session_time')
    def session_time_must_be_future(cls, v):
        if v <= datetime.utcnow():
            raise ValueError('Session time must be in the future')
        return v


class BookingUpdate(SQLModel):
    session_time: Optional[datetime] = Field(default=None, description="New session date and time")
    duration: Optional[int] = Field(default=None, ge=15, le=480, description="New session duration in minutes")
    notes: Optional[str] = Field(default=None, max_length=1000, description="Updated notes")
    session_type: Optional[str] = Field(default=None, max_length=100, description="Updated session type")
    status: Optional[BookingStatus] = Field(default=None, description="Updated booking status")
    meeting_link: Optional[str] = Field(default=None, max_length=500, description="Meeting link for virtual sessions")
    
    @validator('session_time')
    def session_time_must_be_future(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('Session time must be in the future')
        return v


class BookingPublic(BookingBase):
    id: uuid.UUID
    user_id: uuid.UUID
    coach_id: uuid.UUID
    status: BookingStatus
    meeting_link: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Related data
    user: UserPublic
    coach: CoachProfilePublic


class BookingPublicSimple(SQLModel):
    """Simplified booking info for lists"""
    id: uuid.UUID
    session_time: datetime
    duration: int
    status: BookingStatus
    session_type: Optional[str]
    created_at: datetime


class BookingPublicWithUser(BookingPublicSimple):
    """Booking with user info for coaches"""
    user: UserPublic


class BookingPublicWithCoach(BookingPublicSimple):
    """Booking with coach info for users"""
    coach: CoachProfilePublic


class BookingsPublic(PaginatedResponse):
    data: list[BookingPublic]


class BookingsPublicSimple(PaginatedResponse):
    data: list[BookingPublicSimple]


class BookingsPublicWithUser(PaginatedResponse):
    data: list[BookingPublicWithUser]


class BookingsPublicWithCoach(PaginatedResponse):
    data: list[BookingPublicWithCoach]


class BookingStatusUpdate(SQLModel):
    status: BookingStatus = Field(description="New booking status")
    meeting_link: Optional[str] = Field(default=None, max_length=500, description="Meeting link for confirmed sessions")
