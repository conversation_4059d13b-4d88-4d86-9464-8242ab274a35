import uuid
from datetime import datetime
from typing import Optional

from pydantic import Field, HttpUrl
from sqlmodel import SQLModel

from app.models.payment import PaymentMethod, PaymentStatus
from app.schemas.common import PaginatedResponse


class PaymentBase(SQLModel):
    amount: float = Field(ge=0, description="Payment amount in USD")
    currency: str = Field(default="usd", max_length=3, description="Currency code")
    payment_method: PaymentMethod = Field(default=PaymentMethod.STRIPE, description="Payment method")
    description: Optional[str] = Field(default=None, max_length=500, description="Payment description")


class PaymentCreate(PaymentBase):
    booking_id: uuid.UUID = Field(description="ID of the booking to pay for")


class PaymentUpdate(SQLModel):
    status: Optional[PaymentStatus] = Field(default=None, description="Updated payment status")
    failure_reason: Optional[str] = Field(default=None, max_length=500, description="Failure reason")
    stripe_payment_intent_id: Optional[str] = Field(default=None, max_length=255)
    stripe_checkout_session_id: Optional[str] = Field(default=None, max_length=255)
    stripe_customer_id: Optional[str] = Field(default=None, max_length=255)
    paid_at: Optional[datetime] = Field(default=None, description="Payment completion time")


class PaymentPublic(PaymentBase):
    id: uuid.UUID
    booking_id: uuid.UUID
    user_id: uuid.UUID
    status: PaymentStatus
    stripe_payment_intent_id: Optional[str]
    stripe_checkout_session_id: Optional[str]
    failure_reason: Optional[str]
    refund_amount: Optional[float]
    refund_reason: Optional[str]
    created_at: datetime
    updated_at: datetime
    paid_at: Optional[datetime]


class PaymentsPublic(PaginatedResponse):
    data: list[PaymentPublic]


# Stripe-specific schemas
class StripeCheckoutRequest(SQLModel):
    booking_id: uuid.UUID = Field(description="ID of the booking to pay for")
    success_url: Optional[HttpUrl] = Field(default=None, description="Custom success URL")
    cancel_url: Optional[HttpUrl] = Field(default=None, description="Custom cancel URL")


class StripeCheckoutResponse(SQLModel):
    checkout_url: str = Field(description="Stripe checkout URL to redirect user to")
    session_id: str = Field(description="Stripe checkout session ID")
    payment_id: uuid.UUID = Field(description="Internal payment record ID")


class StripeWebhookPayload(SQLModel):
    """Schema for Stripe webhook payload validation"""
    id: str = Field(description="Stripe event ID")
    type: str = Field(description="Stripe event type")
    data: dict = Field(description="Event data from Stripe")
    created: int = Field(description="Event creation timestamp")


# Payment refund schemas
class PaymentRefundRequest(SQLModel):
    amount: Optional[float] = Field(default=None, ge=0, description="Amount to refund (full refund if not specified)")
    reason: str = Field(max_length=500, description="Reason for refund")


class PaymentRefundResponse(SQLModel):
    payment_id: uuid.UUID = Field(description="Payment ID")
    refund_amount: float = Field(description="Amount refunded")
    refund_reason: str = Field(description="Reason for refund")
    refund_id: str = Field(description="Stripe refund ID")
    status: PaymentStatus = Field(description="Updated payment status")


# Payment summary schemas for analytics
class PaymentSummary(SQLModel):
    total_payments: int = Field(description="Total number of payments")
    total_amount: float = Field(description="Total payment amount")
    successful_payments: int = Field(description="Number of successful payments")
    failed_payments: int = Field(description="Number of failed payments")
    pending_payments: int = Field(description="Number of pending payments")
    refunded_amount: float = Field(description="Total refunded amount")


class CoachPaymentSummary(PaymentSummary):
    coach_id: uuid.UUID = Field(description="Coach ID")
    coach_name: str = Field(description="Coach name")
    average_session_rate: float = Field(description="Average session rate")


# Webhook event schemas
class StripeWebhookEventPublic(SQLModel):
    id: uuid.UUID
    stripe_event_id: str
    event_type: str
    processed: bool
    processing_error: Optional[str]
    created_at: datetime
    processed_at: Optional[datetime]


class StripeWebhookEventsPublic(PaginatedResponse):
    data: list[StripeWebhookEventPublic]
