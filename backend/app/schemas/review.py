import uuid
from datetime import datetime

from pydantic import Field
from sqlmodel import SQLModel

from app.schemas.common import PaginatedResponse
from app.schemas.user import UserPublic


class ReviewBase(SQLModel):
    rating: int = Field(ge=1, le=5, description="Rating from 1-5 stars")
    comment: str | None = Field(default=None, description="Detailed review/testimonial comment")
    session_type: str | None = Field(default=None, max_length=100, description="Type of session")


class ReviewCreate(ReviewBase):
    coach_profile_id: uuid.UUID


class ReviewUpdate(SQLModel):
    rating: int | None = Field(default=None, ge=1, le=5)
    comment: str | None = None
    is_featured: bool | None = None
    is_public: bool | None = None
    session_type: str | None = None


class ReviewPublic(ReviewBase):
    id: uuid.UUID
    coach_profile_id: uuid.UUID
    user_id: uuid.UUID
    is_featured: bool
    is_public: bool
    created_at: datetime
    updated_at: datetime
    user: UserPublic


class ReviewsPublic(PaginatedResponse):
    data: list[ReviewPublic]


# Testimonial-specific schemas (using the same Review model)
class TestimonialPublic(ReviewPublic):
    """Alias for ReviewPublic to emphasize testimonial context"""
    pass


class TestimonialsPublic(PaginatedResponse):
    data: list[TestimonialPublic]


class FeaturedTestimonialsResponse(SQLModel):
    """Response for featured testimonials on homepage/marketing"""
    testimonials: list[TestimonialPublic]
    total_count: int
    average_rating: float | None
