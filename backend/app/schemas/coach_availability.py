import uuid
from datetime import datetime, time
from typing import List, Optional

from pydantic import Field, validator
from sqlmodel import SQLModel

from app.models.coach_availability import AvailabilityType, DayOfWeek
from app.schemas.common import PaginatedResponse


class CoachAvailabilityBase(SQLModel):
    day_of_week: DayOfWeek = Field(description="Day of the week")
    start_time: time = Field(description="Start time for availability")
    end_time: time = Field(description="End time for availability")
    availability_type: AvailabilityType = Field(
        default=AvailabilityType.RECURRING,
        description="Type of availability"
    )
    specific_date: Optional[datetime] = Field(
        default=None,
        description="Specific date for exceptions"
    )
    timezone: str = Field(default="UTC", description="Timezone for availability")
    notes: Optional[str] = Field(default=None, max_length=500, description="Notes")

    @validator('end_time')
    def end_time_after_start_time(cls, v, values):
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('End time must be after start time')
        return v

    @validator('specific_date')
    def specific_date_for_exceptions(cls, v, values):
        if 'availability_type' in values:
            if values['availability_type'] == AvailabilityType.EXCEPTION and not v:
                raise ValueError('Specific date is required for exception type')
            if values['availability_type'] == AvailabilityType.RECURRING and v:
                raise ValueError('Specific date should not be set for recurring type')
        return v


class CoachAvailabilityCreate(CoachAvailabilityBase):
    pass


class CoachAvailabilityUpdate(SQLModel):
    day_of_week: Optional[DayOfWeek] = Field(default=None)
    start_time: Optional[time] = Field(default=None)
    end_time: Optional[time] = Field(default=None)
    availability_type: Optional[AvailabilityType] = Field(default=None)
    specific_date: Optional[datetime] = Field(default=None)
    timezone: Optional[str] = Field(default=None)
    is_active: Optional[bool] = Field(default=None)
    notes: Optional[str] = Field(default=None, max_length=500)


class CoachAvailabilityPublic(CoachAvailabilityBase):
    id: uuid.UUID
    coach_id: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime


class CoachAvailabilitiesPublic(PaginatedResponse):
    data: List[CoachAvailabilityPublic]


# Time slot schemas for availability checking
class TimeSlotRequest(SQLModel):
    date: datetime = Field(description="Date to check availability")
    duration_minutes: int = Field(
        ge=15, le=480, default=60,
        description="Duration of the session in minutes"
    )
    timezone: str = Field(default="UTC", description="Timezone for the request")


class AvailableTimeSlot(SQLModel):
    start_time: datetime = Field(description="Start time of the available slot")
    end_time: datetime = Field(description="End time of the available slot")
    duration_minutes: int = Field(description="Duration in minutes")
    is_available: bool = Field(default=True, description="Whether the slot is available")
    timezone: str = Field(description="Timezone of the slot")


class AvailableTimeSlotsResponse(SQLModel):
    date: datetime = Field(description="Date for the availability")
    coach_id: uuid.UUID = Field(description="Coach ID")
    timezone: str = Field(description="Timezone for all slots")
    slots: List[AvailableTimeSlot] = Field(description="Available time slots")
    total_slots: int = Field(description="Total number of available slots")


class WeeklyAvailabilityRequest(SQLModel):
    start_date: datetime = Field(description="Start date of the week")
    duration_minutes: int = Field(
        ge=15, le=480, default=60,
        description="Duration of sessions to check"
    )
    timezone: str = Field(default="UTC", description="Timezone for the request")


class WeeklyAvailabilityResponse(SQLModel):
    coach_id: uuid.UUID = Field(description="Coach ID")
    start_date: datetime = Field(description="Start date of the week")
    end_date: datetime = Field(description="End date of the week")
    timezone: str = Field(description="Timezone for all slots")
    daily_availability: dict[str, List[AvailableTimeSlot]] = Field(
        description="Daily availability mapped by date string (YYYY-MM-DD)"
    )
    total_slots: int = Field(description="Total available slots for the week")


# Bulk operations
class BulkAvailabilityCreate(SQLModel):
    availabilities: List[CoachAvailabilityCreate] = Field(
        description="List of availability slots to create"
    )


class BulkAvailabilityResponse(SQLModel):
    created: List[CoachAvailabilityPublic] = Field(description="Successfully created slots")
    errors: List[dict] = Field(description="Errors for failed creations")
    total_created: int = Field(description="Number of successfully created slots")
    total_errors: int = Field(description="Number of failed creations")
