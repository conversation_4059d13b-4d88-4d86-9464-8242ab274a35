import uuid
from datetime import datetime
from typing import Optional

from sqlmodel import Session, and_, or_, select

from app.models.booking import Booking, BookingStatus
from app.models.coach import CoachProfile
from app.models.user import User
from app.repositories.base_repository import BaseRepository
from app.schemas.booking import BookingCreate, BookingUpdate


class BookingRepository(BaseRepository[Booking, BookingCreate, BookingUpdate]):
    """Repository for Booking model with booking-specific methods."""
    
    def __init__(self):
        super().__init__(Booking)
    
    def get_user_bookings(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> list[Booking]:
        """Get bookings for a specific user."""
        statement = (
            select(Booking)
            .join(CoachProfile, Booking.coach_id == CoachProfile.id)
            .join(User, CoachProfile.user_id == User.id)
            .where(Booking.user_id == user_id)
        )
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        if upcoming_only:
            statement = statement.where(Booking.session_time > datetime.utcnow())
        
        statement = statement.order_by(Booking.session_time.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_coach_bookings(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> list[Booking]:
        """Get bookings for a specific coach."""
        statement = (
            select(Booking)
            .join(User, Booking.user_id == User.id)
            .where(Booking.coach_id == coach_id)
        )
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        if upcoming_only:
            statement = statement.where(Booking.session_time > datetime.utcnow())
        
        statement = statement.order_by(Booking.session_time.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_bookings_by_date_range(
        self,
        session: Session,
        *,
        coach_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        start_date: datetime,
        end_date: datetime,
        status: Optional[BookingStatus] = None
    ) -> list[Booking]:
        """Get bookings within a specific date range."""
        statement = select(Booking).where(
            and_(
                Booking.session_time >= start_date,
                Booking.session_time <= end_date
            )
        )
        
        if coach_id:
            statement = statement.where(Booking.coach_id == coach_id)
        
        if user_id:
            statement = statement.where(Booking.user_id == user_id)
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        statement = statement.order_by(Booking.session_time)
        return session.exec(statement).all()
    
    def check_coach_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        session_time: datetime,
        duration: int,
        exclude_booking_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Check if a coach is available at a specific time."""
        session_end = session_time.replace(
            minute=session_time.minute + duration
        )
        
        # Check for overlapping bookings
        statement = select(Booking).where(
            and_(
                Booking.coach_id == coach_id,
                Booking.status.in_([BookingStatus.CONFIRMED.value, BookingStatus.PENDING.value]),
                or_(
                    # New session starts during existing session
                    and_(
                        Booking.session_time <= session_time,
                        Booking.session_time + Booking.duration * 60 > session_time  # Convert minutes to seconds
                    ),
                    # New session ends during existing session
                    and_(
                        Booking.session_time < session_end,
                        Booking.session_time + Booking.duration * 60 >= session_end
                    ),
                    # New session completely contains existing session
                    and_(
                        session_time <= Booking.session_time,
                        session_end >= Booking.session_time + Booking.duration * 60
                    )
                )
            )
        )
        
        if exclude_booking_id:
            statement = statement.where(Booking.id != exclude_booking_id)
        
        conflicting_bookings = session.exec(statement).all()
        return len(conflicting_bookings) == 0
    
    def get_upcoming_bookings(
        self,
        session: Session,
        *,
        hours_ahead: int = 24,
        status: Optional[BookingStatus] = None
    ) -> list[Booking]:
        """Get bookings that are coming up within specified hours."""
        from datetime import timedelta
        
        now = datetime.utcnow()
        future_time = now + timedelta(hours=hours_ahead)
        
        statement = select(Booking).where(
            and_(
                Booking.session_time >= now,
                Booking.session_time <= future_time
            )
        )
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        statement = statement.order_by(Booking.session_time)
        return session.exec(statement).all()
    
    def count_user_bookings(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False
    ) -> int:
        """Count bookings for a specific user."""
        statement = select(Booking).where(Booking.user_id == user_id)
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        if upcoming_only:
            statement = statement.where(Booking.session_time > datetime.utcnow())
        
        return len(session.exec(statement).all())
    
    def count_coach_bookings(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        status: Optional[BookingStatus] = None,
        upcoming_only: bool = False
    ) -> int:
        """Count bookings for a specific coach."""
        statement = select(Booking).where(Booking.coach_id == coach_id)
        
        if status:
            statement = statement.where(Booking.status == status.value)
        
        if upcoming_only:
            statement = statement.where(Booking.session_time > datetime.utcnow())
        
        return len(session.exec(statement).all())
