import uuid
from datetime import datetime, timedelta
from typing import List, Optional

from sqlmodel import Session, and_, or_, select

from app.models.booking import Booking, BookingStatus
from app.models.coach_availability import AvailabilityType, CoachAvailability, DayOfWeek
from app.repositories.base_repository import BaseRepository
from app.schemas.coach_availability import CoachAvailabilityCreate, CoachAvailabilityUpdate


class CoachAvailabilityRepository(BaseRepository[CoachAvailability, CoachAvailabilityCreate, CoachAvailabilityUpdate]):
    """Repository for CoachAvailability model with availability-specific methods."""
    
    def __init__(self):
        super().__init__(CoachAvailability)
    
    def get_coach_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        day_of_week: Optional[DayOfWeek] = None,
        availability_type: Optional[AvailabilityType] = None,
        is_active: bool = True
    ) -> List[CoachAvailability]:
        """Get availability slots for a coach."""
        statement = select(CoachAvailability).where(
            and_(
                CoachAvailability.coach_id == coach_id,
                CoachAvailability.is_active == is_active
            )
        )
        
        if day_of_week:
            statement = statement.where(CoachAvailability.day_of_week == day_of_week.value)
        
        if availability_type:
            statement = statement.where(CoachAvailability.availability_type == availability_type.value)
        
        statement = statement.order_by(
            CoachAvailability.day_of_week,
            CoachAvailability.start_time
        )
        
        return session.exec(statement).all()
    
    def get_availability_for_date(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        target_date: datetime,
        is_active: bool = True
    ) -> List[CoachAvailability]:
        """Get availability for a specific date, considering exceptions."""
        day_name = target_date.strftime('%A').lower()
        
        # First, get any specific date exceptions
        exceptions = session.exec(
            select(CoachAvailability).where(
                and_(
                    CoachAvailability.coach_id == coach_id,
                    CoachAvailability.specific_date == target_date.date(),
                    CoachAvailability.is_active == is_active
                )
            )
        ).all()
        
        if exceptions:
            # If there are exceptions for this date, use them instead of recurring
            return [exc for exc in exceptions if exc.availability_type != AvailabilityType.BLOCKED.value]
        
        # Otherwise, get recurring availability for this day
        recurring = session.exec(
            select(CoachAvailability).where(
                and_(
                    CoachAvailability.coach_id == coach_id,
                    CoachAvailability.day_of_week == day_name,
                    CoachAvailability.availability_type == AvailabilityType.RECURRING.value,
                    CoachAvailability.is_active == is_active
                )
            )
        ).all()
        
        return recurring
    
    def get_existing_bookings_for_date(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        target_date: datetime
    ) -> List[Booking]:
        """Get existing bookings for a coach on a specific date."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)
        
        statement = select(Booking).where(
            and_(
                Booking.coach_id == coach_id,
                Booking.session_time >= start_of_day,
                Booking.session_time < end_of_day,
                Booking.status.in_([
                    BookingStatus.PENDING.value,
                    BookingStatus.CONFIRMED.value
                ])
            )
        ).order_by(Booking.session_time)
        
        return session.exec(statement).all()
    
    def create_bulk_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        availabilities: List[CoachAvailabilityCreate]
    ) -> tuple[List[CoachAvailability], List[dict]]:
        """Create multiple availability slots in bulk."""
        created = []
        errors = []
        
        for i, availability_data in enumerate(availabilities):
            try:
                # Check for conflicts with existing availability
                existing = self.get_coach_availability(
                    session,
                    coach_id=coach_id,
                    day_of_week=DayOfWeek(availability_data.day_of_week),
                    availability_type=AvailabilityType(availability_data.availability_type)
                )
                
                # Check for time overlaps
                has_conflict = any(
                    self._times_overlap(
                        availability_data.start_time,
                        availability_data.end_time,
                        existing_slot.start_time,
                        existing_slot.end_time
                    )
                    for existing_slot in existing
                )
                
                if has_conflict:
                    errors.append({
                        "index": i,
                        "error": "Time slot conflicts with existing availability",
                        "data": availability_data.model_dump()
                    })
                    continue
                
                # Create the availability slot
                availability_dict = availability_data.model_dump()
                availability_dict["coach_id"] = coach_id
                
                db_availability = CoachAvailability(**availability_dict)
                session.add(db_availability)
                session.flush()  # Flush to get the ID
                session.refresh(db_availability)
                created.append(db_availability)
                
            except Exception as e:
                errors.append({
                    "index": i,
                    "error": str(e),
                    "data": availability_data.model_dump()
                })
        
        if created:
            session.commit()
        
        return created, errors
    
    def delete_coach_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        availability_ids: List[uuid.UUID]
    ) -> int:
        """Delete multiple availability slots for a coach."""
        statement = select(CoachAvailability).where(
            and_(
                CoachAvailability.coach_id == coach_id,
                CoachAvailability.id.in_(availability_ids)
            )
        )
        
        availabilities = session.exec(statement).all()
        count = len(availabilities)
        
        for availability in availabilities:
            session.delete(availability)
        
        session.commit()
        return count
    
    def get_weekly_availability(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime
    ) -> List[CoachAvailability]:
        """Get availability for a date range (typically a week)."""
        # Get all recurring availability
        recurring = self.get_coach_availability(
            session,
            coach_id=coach_id,
            availability_type=AvailabilityType.RECURRING
        )
        
        # Get any exceptions in the date range
        exceptions = session.exec(
            select(CoachAvailability).where(
                and_(
                    CoachAvailability.coach_id == coach_id,
                    CoachAvailability.specific_date >= start_date.date(),
                    CoachAvailability.specific_date <= end_date.date(),
                    CoachAvailability.is_active == True
                )
            )
        ).all()
        
        return recurring + exceptions
    
    @staticmethod
    def _times_overlap(start1, end1, start2, end2) -> bool:
        """Check if two time ranges overlap."""
        return start1 < end2 and end1 > start2
