import uuid
from typing import List, Optional

from sqlmodel import Session, and_, select

from app.models.payment import Payment, PaymentStatus
from app.repositories.base_repository import BaseRepository
from app.schemas.payment import PaymentCreate, PaymentUpdate


class PaymentRepository(BaseRepository[Payment, PaymentCreate, PaymentUpdate]):
    """Repository for Payment model with payment-specific methods."""
    
    def __init__(self):
        super().__init__(Payment)
    
    def get_by_booking_id(
        self,
        session: Session,
        *,
        booking_id: uuid.UUID
    ) -> Optional[Payment]:
        """Get payment by booking ID."""
        statement = select(Payment).where(Payment.booking_id == booking_id)
        return session.exec(statement).first()
    
    def get_by_stripe_session_id(
        self,
        session: Session,
        *,
        session_id: str
    ) -> Optional[Payment]:
        """Get payment by Stripe checkout session ID."""
        statement = select(Payment).where(Payment.stripe_checkout_session_id == session_id)
        return session.exec(statement).first()
    
    def get_by_stripe_payment_intent_id(
        self,
        session: Session,
        *,
        payment_intent_id: str
    ) -> Optional[Payment]:
        """Get payment by Stripe payment intent ID."""
        statement = select(Payment).where(Payment.stripe_payment_intent_id == payment_intent_id)
        return session.exec(statement).first()
    
    def get_user_payments(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        status: Optional[PaymentStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """Get payments for a specific user."""
        statement = select(Payment).where(Payment.user_id == user_id)
        
        if status:
            statement = statement.where(Payment.status == status.value)
        
        statement = statement.order_by(Payment.created_at.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_coach_payments(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        status: Optional[PaymentStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """Get payments for a specific coach's sessions."""
        from app.models.booking import Booking
        
        statement = (
            select(Payment)
            .join(Booking, Payment.booking_id == Booking.id)
            .where(Booking.coach_id == coach_id)
        )
        
        if status:
            statement = statement.where(Payment.status == status.value)
        
        statement = statement.order_by(Payment.created_at.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_payments_by_status(
        self,
        session: Session,
        *,
        status: PaymentStatus,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """Get payments by status."""
        statement = (
            select(Payment)
            .where(Payment.status == status.value)
            .order_by(Payment.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def count_user_payments(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        status: Optional[PaymentStatus] = None
    ) -> int:
        """Count payments for a specific user."""
        statement = select(Payment).where(Payment.user_id == user_id)
        
        if status:
            statement = statement.where(Payment.status == status.value)
        
        return len(session.exec(statement).all())
    
    def count_coach_payments(
        self,
        session: Session,
        *,
        coach_id: uuid.UUID,
        status: Optional[PaymentStatus] = None
    ) -> int:
        """Count payments for a specific coach's sessions."""
        from app.models.booking import Booking
        
        statement = (
            select(Payment)
            .join(Booking, Payment.booking_id == Booking.id)
            .where(Booking.coach_id == coach_id)
        )
        
        if status:
            statement = statement.where(Payment.status == status.value)
        
        return len(session.exec(statement).all())
    
    def get_payment_summary(
        self,
        session: Session,
        *,
        user_id: Optional[uuid.UUID] = None,
        coach_id: Optional[uuid.UUID] = None
    ) -> dict:
        """Get payment summary statistics."""
        from app.models.booking import Booking
        
        statement = select(Payment)
        
        if user_id:
            statement = statement.where(Payment.user_id == user_id)
        elif coach_id:
            statement = statement.join(Booking, Payment.booking_id == Booking.id).where(Booking.coach_id == coach_id)
        
        payments = session.exec(statement).all()
        
        total_payments = len(payments)
        total_amount = sum(p.amount for p in payments)
        successful_payments = len([p for p in payments if p.status == PaymentStatus.SUCCEEDED.value])
        failed_payments = len([p for p in payments if p.status == PaymentStatus.FAILED.value])
        pending_payments = len([p for p in payments if p.status == PaymentStatus.PENDING.value])
        refunded_amount = sum(p.refund_amount or 0 for p in payments)
        
        return {
            "total_payments": total_payments,
            "total_amount": total_amount,
            "successful_payments": successful_payments,
            "failed_payments": failed_payments,
            "pending_payments": pending_payments,
            "refunded_amount": refunded_amount
        }
