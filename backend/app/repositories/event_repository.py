import uuid
from datetime import datetime
from typing import Optional

from sqlmodel import Session, and_, func, or_, select

from app.models.event import Event, EventRSVP, EventStatus, EventType, RSVPStatus
from app.repositories.base_repository import BaseRepository
from app.schemas.event import EventCreate, EventSearchFilters, EventUpdate


class EventRepository(BaseRepository[Event, EventCreate, EventUpdate]):
    """Repository for Event model with event-specific methods."""
    
    def __init__(self):
        super().__init__(Event)
    
    def get_by_host(
        self,
        session: Session,
        *,
        host_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[EventStatus] = None
    ) -> list[Event]:
        """Get events by host (coach)."""
        statement = select(Event).where(Event.host_id == host_id)
        
        if status:
            statement = statement.where(Event.status == status.value)
        
        statement = statement.order_by(Event.event_date.desc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def search_events(
        self,
        session: Session,
        *,
        filters: EventSearchFilters,
        skip: int = 0,
        limit: int = 100
    ) -> tuple[list[Event], int]:
        """Search events with filters."""
        statement = select(Event).where(Event.status == EventStatus.PUBLISHED.value)
        count_statement = select(func.count(Event.id)).where(Event.status == EventStatus.PUBLISHED.value)
        
        # Apply filters
        if filters.event_type:
            statement = statement.where(Event.event_type == filters.event_type.value)
            count_statement = count_statement.where(Event.event_type == filters.event_type.value)
        
        if filters.category:
            statement = statement.where(Event.category.ilike(f"%{filters.category}%"))
            count_statement = count_statement.where(Event.category.ilike(f"%{filters.category}%"))
        
        if filters.location_id:
            statement = statement.where(Event.location_id == filters.location_id)
            count_statement = count_statement.where(Event.location_id == filters.location_id)
        
        if filters.host_id:
            statement = statement.where(Event.host_id == filters.host_id)
            count_statement = count_statement.where(Event.host_id == filters.host_id)
        
        if filters.min_price is not None:
            statement = statement.where(Event.price >= filters.min_price)
            count_statement = count_statement.where(Event.price >= filters.min_price)
        
        if filters.max_price is not None:
            statement = statement.where(Event.price <= filters.max_price)
            count_statement = count_statement.where(Event.price <= filters.max_price)
        
        if filters.start_date:
            statement = statement.where(Event.event_date >= filters.start_date)
            count_statement = count_statement.where(Event.event_date >= filters.start_date)
        
        if filters.end_date:
            statement = statement.where(Event.event_date <= filters.end_date)
            count_statement = count_statement.where(Event.event_date <= filters.end_date)
        
        if filters.has_spots_available:
            statement = statement.where(Event.current_attendees < Event.capacity)
            count_statement = count_statement.where(Event.current_attendees < Event.capacity)
        
        if filters.is_free is not None:
            if filters.is_free:
                statement = statement.where(Event.price == 0)
                count_statement = count_statement.where(Event.price == 0)
            else:
                statement = statement.where(Event.price > 0)
                count_statement = count_statement.where(Event.price > 0)
        
        if filters.tags:
            statement = statement.where(Event.tags.ilike(f"%{filters.tags}%"))
            count_statement = count_statement.where(Event.tags.ilike(f"%{filters.tags}%"))
        
        # Order by event date
        statement = statement.order_by(Event.event_date.asc()).offset(skip).limit(limit)
        
        events = session.exec(statement).all()
        count = session.exec(count_statement).one()
        
        return events, count
    
    def get_upcoming_events(
        self,
        session: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        host_id: Optional[uuid.UUID] = None
    ) -> list[Event]:
        """Get upcoming published events."""
        now = datetime.utcnow()
        statement = select(Event).where(
            and_(
                Event.status == EventStatus.PUBLISHED.value,
                Event.event_date > now
            )
        )
        
        if host_id:
            statement = statement.where(Event.host_id == host_id)
        
        statement = statement.order_by(Event.event_date.asc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_events_by_date_range(
        self,
        session: Session,
        *,
        start_date: datetime,
        end_date: datetime,
        host_id: Optional[uuid.UUID] = None
    ) -> list[Event]:
        """Get events within a date range."""
        statement = select(Event).where(
            and_(
                Event.event_date >= start_date,
                Event.event_date <= end_date,
                Event.status == EventStatus.PUBLISHED.value
            )
        )
        
        if host_id:
            statement = statement.where(Event.host_id == host_id)
        
        statement = statement.order_by(Event.event_date.asc())
        return session.exec(statement).all()
    
    def update_attendee_count(
        self,
        session: Session,
        *,
        event_id: uuid.UUID
    ) -> Optional[Event]:
        """Update the current attendee count for an event."""
        event = session.get(Event, event_id)
        if not event:
            return None
        
        # Count confirmed RSVPs
        confirmed_count = session.exec(
            select(func.count(EventRSVP.id)).where(
                and_(
                    EventRSVP.event_id == event_id,
                    EventRSVP.status == RSVPStatus.CONFIRMED.value,
                    EventRSVP.is_waitlisted == False
                )
            )
        ).one()
        
        event.current_attendees = confirmed_count
        session.commit()
        session.refresh(event)
        
        return event
    
    def get_popular_events(
        self,
        session: Session,
        *,
        limit: int = 10
    ) -> list[Event]:
        """Get popular events based on attendance."""
        now = datetime.utcnow()
        statement = (
            select(Event)
            .where(
                and_(
                    Event.status == EventStatus.PUBLISHED.value,
                    Event.event_date > now
                )
            )
            .order_by(Event.current_attendees.desc(), Event.event_date.asc())
            .limit(limit)
        )
        return session.exec(statement).all()


class EventRSVPRepository(BaseRepository[EventRSVP, dict, dict]):
    """Repository for EventRSVP model."""
    
    def __init__(self):
        super().__init__(EventRSVP)
    
    def get_by_user_and_event(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        event_id: uuid.UUID
    ) -> Optional[EventRSVP]:
        """Get RSVP by user and event."""
        statement = select(EventRSVP).where(
            and_(
                EventRSVP.user_id == user_id,
                EventRSVP.event_id == event_id
            )
        )
        return session.exec(statement).first()
    
    def get_by_event(
        self,
        session: Session,
        *,
        event_id: uuid.UUID,
        status: Optional[RSVPStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[EventRSVP]:
        """Get RSVPs for an event."""
        statement = select(EventRSVP).where(EventRSVP.event_id == event_id)
        
        if status:
            statement = statement.where(EventRSVP.status == status.value)
        
        statement = statement.order_by(EventRSVP.created_at.asc()).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_by_user(
        self,
        session: Session,
        *,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[EventRSVP]:
        """Get RSVPs by user."""
        statement = (
            select(EventRSVP)
            .where(EventRSVP.user_id == user_id)
            .order_by(EventRSVP.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def get_waitlist(
        self,
        session: Session,
        *,
        event_id: uuid.UUID
    ) -> list[EventRSVP]:
        """Get waitlisted RSVPs for an event."""
        statement = (
            select(EventRSVP)
            .where(
                and_(
                    EventRSVP.event_id == event_id,
                    EventRSVP.is_waitlisted == True
                )
            )
            .order_by(EventRSVP.waitlist_position.asc())
        )
        return session.exec(statement).all()
    
    def get_next_waitlist_position(
        self,
        session: Session,
        *,
        event_id: uuid.UUID
    ) -> int:
        """Get the next waitlist position for an event."""
        max_position = session.exec(
            select(func.max(EventRSVP.waitlist_position)).where(
                and_(
                    EventRSVP.event_id == event_id,
                    EventRSVP.is_waitlisted == True
                )
            )
        ).one()
        
        return (max_position or 0) + 1
    
    def promote_from_waitlist(
        self,
        session: Session,
        *,
        event_id: uuid.UUID,
        spots_available: int = 1
    ) -> list[EventRSVP]:
        """Promote users from waitlist to confirmed."""
        waitlisted_rsvps = (
            session.exec(
                select(EventRSVP)
                .where(
                    and_(
                        EventRSVP.event_id == event_id,
                        EventRSVP.is_waitlisted == True,
                        EventRSVP.status == RSVPStatus.PENDING.value
                    )
                )
                .order_by(EventRSVP.waitlist_position.asc())
                .limit(spots_available)
            )
            .all()
        )
        
        promoted_rsvps = []
        for rsvp in waitlisted_rsvps:
            rsvp.is_waitlisted = False
            rsvp.waitlist_position = None
            rsvp.status = RSVPStatus.CONFIRMED.value
            promoted_rsvps.append(rsvp)
        
        session.commit()
        return promoted_rsvps
