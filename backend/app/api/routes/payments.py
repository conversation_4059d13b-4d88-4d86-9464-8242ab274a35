import uuid
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlmodel import Session

from app.api.deps import CurrentUser, get_db
from app.models.payment import PaymentStatus
from app.repositories.payment_repository import PaymentRepository
from app.schemas.common import Message
from app.schemas.payment import (
    PaymentPublic,
    PaymentsPublic,
    PaymentRefundRequest,
    PaymentRefundResponse,
    PaymentSummary,
    StripeCheckoutRequest,
    StripeCheckoutResponse,
)
from app.services.stripe_service import StripeService

router = APIRouter()
payment_repo = PaymentRepository()
stripe_service = StripeService()


@router.post("/stripe/create-checkout", response_model=StripeCheckoutResponse)
def create_stripe_checkout(
    *,
    current_user: CurrentUser,
    checkout_request: StripeCheckoutRequest,
    session: Session = Depends(get_db),
) -> Any:
    """
    Create Stripe checkout session for booking payment.
    """
    return stripe_service.create_checkout_session(
        session=session,
        user_id=current_user.id,
        checkout_request=checkout_request
    )


@router.post("/stripe/webhook")
async def stripe_webhook(
    request: Request,
    session: Session = Depends(get_db),
) -> Any:
    """
    Handle Stripe webhook events.
    """
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')

    if not sig_header:
        raise HTTPException(status_code=400, detail="Missing stripe-signature header")

    return stripe_service.handle_webhook_event(
        session=session,
        payload=payload.decode('utf-8'),
        sig_header=sig_header
    )


@router.get("/my-payments", response_model=PaymentsPublic)
def get_my_payments(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
    status: PaymentStatus | None = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Get current user's payments.
    """
    payments = payment_repo.get_user_payments(
        session=session,
        user_id=current_user.id,
        status=status,
        skip=skip,
        limit=limit
    )

    count = payment_repo.count_user_payments(
        session=session,
        user_id=current_user.id,
        status=status
    )

    return PaymentsPublic(count=count, data=payments)


@router.get("/coach-payments", response_model=PaymentsPublic)
def get_coach_payments(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
    status: PaymentStatus | None = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Get payments for current user's coach profile.
    Only accessible by coaches.
    """
    # Verify user is a coach
    if current_user.role != "coach":
        raise HTTPException(status_code=403, detail="Only coaches can access this endpoint")

    # Get coach profile
    from app.repositories.coach_repository import CoachRepository
    coach_repo = CoachRepository()
    coach_profile = coach_repo.get_by_user_id(session, current_user.id)

    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    payments = payment_repo.get_coach_payments(
        session=session,
        coach_id=coach_profile.id,
        status=status,
        skip=skip,
        limit=limit
    )

    count = payment_repo.count_coach_payments(
        session=session,
        coach_id=coach_profile.id,
        status=status
    )

    return PaymentsPublic(count=count, data=payments)


@router.get("/{payment_id}", response_model=PaymentPublic)
def get_payment(
    payment_id: uuid.UUID,
    current_user: CurrentUser,
    session: Session = Depends(get_db),
) -> Any:
    """
    Get a specific payment by ID.
    Users can only access their own payments.
    Coaches can access payments for their sessions.
    """
    payment = payment_repo.get(session, payment_id)
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    # Check authorization
    if payment.user_id == current_user.id:
        # User accessing their own payment
        return payment
    elif current_user.role == "coach":
        # Coach accessing payment for their session
        if payment.booking.coach.user_id == current_user.id:
            return payment
    elif current_user.role == "admin":
        # Admin can access any payment
        return payment

    raise HTTPException(status_code=403, detail="Not authorized to access this payment")


@router.post("/{payment_id}/refund", response_model=PaymentRefundResponse)
def create_refund(
    payment_id: uuid.UUID,
    refund_request: PaymentRefundRequest,
    current_user: CurrentUser,
    session: Session = Depends(get_db),
) -> Any:
    """
    Create a refund for a payment.
    Only coaches and admins can create refunds.
    """
    payment = payment_repo.get(session, payment_id)
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    # Check authorization
    can_refund = False
    if current_user.role == "admin":
        can_refund = True
    elif current_user.role == "coach" and payment.booking.coach.user_id == current_user.id:
        can_refund = True

    if not can_refund:
        raise HTTPException(status_code=403, detail="Not authorized to refund this payment")

    # Create refund
    refund_result = stripe_service.create_refund(
        session=session,
        payment_id=payment_id,
        amount=refund_request.amount,
        reason=refund_request.reason
    )

    # Refresh payment to get updated data
    session.refresh(payment)

    return PaymentRefundResponse(
        payment_id=payment.id,
        refund_amount=payment.refund_amount,
        refund_reason=payment.refund_reason,
        refund_id=refund_result["refund_id"],
        status=PaymentStatus(payment.status)
    )


@router.get("/summary/my-summary", response_model=PaymentSummary)
def get_my_payment_summary(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
) -> Any:
    """
    Get payment summary for current user.
    """
    summary = payment_repo.get_payment_summary(
        session=session,
        user_id=current_user.id
    )
    return PaymentSummary(**summary)


@router.get("/summary/coach-summary", response_model=PaymentSummary)
def get_coach_payment_summary(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
) -> Any:
    """
    Get payment summary for current user's coach profile.
    Only accessible by coaches.
    """
    # Verify user is a coach
    if current_user.role != "coach":
        raise HTTPException(status_code=403, detail="Only coaches can access this endpoint")

    # Get coach profile
    from app.repositories.coach_repository import CoachRepository
    coach_repo = CoachRepository()
    coach_profile = coach_repo.get_by_user_id(session, current_user.id)

    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    summary = payment_repo.get_payment_summary(
        session=session,
        coach_id=coach_profile.id
    )
    return PaymentSummary(**summary)


@router.get("/test-payments", response_model=dict)
def test_payments_endpoint() -> Any:
    """
    Test endpoint to verify payment routes are working.
    """
    return {"message": "Payment endpoints are working", "status": "ok"}
