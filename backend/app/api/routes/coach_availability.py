import uuid
from datetime import datetime
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session

from app.api.deps import CurrentUser, get_db
from app.models.coach_availability import DayOfWeek, AvailabilityType
from app.models.user import User, UserRole
from app.schemas.coach_availability import (
    AvailableTimeSlotsResponse,
    BulkAvailabilityCreate,
    BulkAvailabilityResponse,
    CoachAvailabilitiesPublic,
    CoachAvailabilityCreate,
    CoachAvailabilityPublic,
    CoachAvailabilityUpdate,
    TimeSlotRequest,
    WeeklyAvailabilityRequest,
    WeeklyAvailabilityResponse,
)
from app.schemas.common import Message
from app.services.coach_availability_service import CoachAvailabilityService
from app.services.coach_service import CoachService

router = APIRouter()
availability_service = CoachAvailabilityService()
coach_service = CoachService()


@router.post("/", response_model=CoachAvailabilityPublic)
def create_availability(
    *,
    current_user: CurrentUser,
    availability_in: CoachAvailabilityCreate,
    session: Session = Depends(get_db),
) -> Any:
    """
    Create a new availability slot.
    Only accessible by coaches for their own profile.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can create availability")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    availability = availability_service.create_availability(
        session=session,
        coach_id=coach_profile.id,
        availability_data=availability_in
    )
    return availability


@router.get("/my-availability", response_model=CoachAvailabilitiesPublic)
def get_my_availability(
    current_user: CurrentUser,
    session: Session = Depends(get_db),
    day_of_week: DayOfWeek = Query(None, description="Filter by day of week"),
    availability_type: AvailabilityType = Query(None, description="Filter by availability type"),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of items to return"),
) -> Any:
    """
    Get current coach's availability slots.
    Only accessible by coaches.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can access this endpoint")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    availabilities = availability_service.get_coach_availability(
        session=session,
        coach_id=coach_profile.id,
        day_of_week=day_of_week,
        availability_type=availability_type
    )

    # Apply pagination
    total_count = len(availabilities)
    paginated_availabilities = availabilities[skip:skip + limit]

    return CoachAvailabilitiesPublic(data=paginated_availabilities, count=total_count)


@router.get("/{coach_id}/available-slots", response_model=AvailableTimeSlotsResponse)
def get_coach_available_slots(
    coach_id: uuid.UUID,
    session: Session = Depends(get_db),
    date: datetime = Query(..., description="Date to check availability (YYYY-MM-DD)"),
    duration_minutes: int = Query(60, ge=15, le=480, description="Session duration in minutes"),
    timezone: str = Query("UTC", description="Timezone for the slots"),
) -> Any:
    """
    Get available time slots for a specific coach on a specific date.
    Public endpoint - anyone can check coach availability.
    """
    available_slots = availability_service.get_available_time_slots(
        session=session,
        coach_id=coach_id,
        target_date=date,
        duration_minutes=duration_minutes,
        timezone=timezone
    )
    return available_slots


@router.get("/{coach_id}/weekly-availability", response_model=WeeklyAvailabilityResponse)
def get_coach_weekly_availability(
    coach_id: uuid.UUID,
    session: Session = Depends(get_db),
    start_date: datetime = Query(..., description="Start date of the week (YYYY-MM-DD)"),
    duration_minutes: int = Query(60, ge=15, le=480, description="Session duration in minutes"),
    timezone: str = Query("UTC", description="Timezone for the slots"),
) -> Any:
    """
    Get weekly availability for a specific coach.
    Public endpoint - anyone can check coach's weekly availability.
    """
    weekly_availability = availability_service.get_weekly_availability(
        session=session,
        coach_id=coach_id,
        start_date=start_date,
        duration_minutes=duration_minutes,
        timezone=timezone
    )
    return weekly_availability


@router.post("/bulk", response_model=BulkAvailabilityResponse)
def create_bulk_availability(
    *,
    current_user: CurrentUser,
    bulk_data: BulkAvailabilityCreate,
    session: Session = Depends(get_db),
) -> Any:
    """
    Create multiple availability slots in bulk.
    Only accessible by coaches for their own profile.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can create availability")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    result = availability_service.create_bulk_availability(
        session=session,
        coach_id=coach_profile.id,
        bulk_data=bulk_data
    )
    return result


@router.put("/{availability_id}", response_model=CoachAvailabilityPublic)
def update_availability(
    *,
    current_user: CurrentUser,
    availability_id: uuid.UUID,
    availability_in: CoachAvailabilityUpdate,
    session: Session = Depends(get_db),
) -> Any:
    """
    Update an availability slot.
    Only the coach who owns the slot can update it.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can update availability")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    availability = availability_service.update_availability(
        session=session,
        availability_id=availability_id,
        coach_id=coach_profile.id,
        availability_data=availability_in
    )
    return availability


@router.delete("/{availability_id}", response_model=Message)
def delete_availability(
    availability_id: uuid.UUID,
    current_user: CurrentUser,
    session: Session = Depends(get_db),
) -> Any:
    """
    Delete an availability slot.
    Only the coach who owns the slot can delete it.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can delete availability")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    availability_service.delete_availability(
        session=session,
        availability_id=availability_id,
        coach_id=coach_profile.id
    )
    return Message(message="Availability slot deleted successfully")


@router.get("/test-availability", response_model=dict)
def test_availability_endpoint() -> dict:
    """Test endpoint to verify availability routes are working."""
    return {"message": "Coach availability endpoints are working", "status": "ok"}
