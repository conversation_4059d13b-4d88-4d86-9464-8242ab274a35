import uuid
from typing import Any

from fastapi import APIRouter, HTTPException

from app.api.deps import Ad<PERSON>User, CurrentUser, SessionDep
from app.schemas.dashboard import AdminDashboardSummary, DashboardResponse
from app.services.dashboard_service import DashboardService

router = APIRouter()
dashboard_service = DashboardService()


@router.get("/me/dashboard", response_model=DashboardResponse)
def get_my_dashboard(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    Get dashboard data for the current user.

    Returns role-specific dashboard:
    - Users: upcoming sessions, reviews left, payment history, favorite coaches
    - Coaches: upcoming sessions, reviews received, earnings, session statistics
    - Admins: platform-wide statistics and recent activity
    """
    try:
        dashboard_data = dashboard_service.get_dashboard_for_user(
            session=session,
            user=current_user
        )
        return dashboard_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate dashboard: {str(e)}")


@router.get("/dashboard/user/{user_id}", response_model=DashboardResponse)
def get_user_dashboard(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    user_id: uuid.UUID,
) -> Any:
    """
    Get dashboard data for a specific user (admin only).

    Allows admins to view any user's dashboard for support purposes.
    """
    # Check if current user is admin
    if current_user.role != "admin":
        raise HTTPException(
            status_code=403,
            detail="Only admins can view other users' dashboards"
        )

    try:
        # Get the target user
        from app.models.user import User
        target_user = session.get(User, user_id)
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")

        dashboard_data = dashboard_service.get_dashboard_for_user(
            session=session,
            user=target_user
        )
        return dashboard_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate dashboard: {str(e)}")


@router.get("/dashboard/stats", response_model=dict)
def get_dashboard_stats(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    Get quick dashboard statistics for the current user.

    Returns lightweight statistics without full dashboard data:
    - Users: total bookings, total spent, reviews left
    - Coaches: total sessions, total earnings, average rating
    - Admins: platform totals
    """
    try:
        dashboard_data = dashboard_service.get_dashboard_for_user(
            session=session,
            user=current_user
        )

        # Extract just the stats based on role
        if dashboard_data.user_dashboard:
            return {
                "role": "user",
                "stats": dashboard_data.user_dashboard.stats.model_dump()
            }
        elif dashboard_data.coach_dashboard:
            return {
                "role": "coach",
                "stats": dashboard_data.coach_dashboard.stats.model_dump()
            }
        elif dashboard_data.admin_dashboard:
            return {
                "role": "admin",
                "stats": {
                    "total_users": dashboard_data.admin_dashboard.total_users,
                    "total_coaches": dashboard_data.admin_dashboard.total_coaches,
                    "total_bookings": dashboard_data.admin_dashboard.total_bookings,
                    "total_payments": dashboard_data.admin_dashboard.total_payments,
                    "total_reviews": dashboard_data.admin_dashboard.total_reviews,
                    "this_month_bookings": dashboard_data.admin_dashboard.this_month_bookings,
                    "this_month_revenue": dashboard_data.admin_dashboard.this_month_revenue,
                    "this_month_new_users": dashboard_data.admin_dashboard.this_month_new_users,
                    "this_month_new_coaches": dashboard_data.admin_dashboard.this_month_new_coaches,
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Invalid dashboard data")

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


@router.get("/dashboard/quick-actions", response_model=dict)
def get_quick_actions(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    Get quick action items for the current user's dashboard.

    Returns actionable items that need user attention:
    - Users: pending payments, upcoming sessions (next 7 days)
    - Coaches: pending session confirmations, recent reviews to respond to
    - Admins: recent activity requiring attention
    """
    try:
        dashboard_data = dashboard_service.get_dashboard_for_user(
            session=session,
            user=current_user
        )

        if dashboard_data.user_dashboard:
            # Filter upcoming sessions to next 7 days
            from datetime import datetime, timedelta
            next_week = datetime.utcnow() + timedelta(days=7)
            upcoming_soon = [
                session for session in dashboard_data.user_dashboard.upcoming_sessions
                if session.session_time <= next_week
            ]

            return {
                "role": "user",
                "pending_payments": dashboard_data.user_dashboard.pending_payments,
                "upcoming_sessions_this_week": upcoming_soon,
                "favorite_coaches": dashboard_data.user_dashboard.favorite_coaches[:3],  # Top 3
            }

        elif dashboard_data.coach_dashboard:
            return {
                "role": "coach",
                "pending_sessions": dashboard_data.coach_dashboard.pending_sessions,
                "featured_testimonials": dashboard_data.coach_dashboard.featured_testimonials,
                "upcoming_sessions_today": [
                    session for session in dashboard_data.coach_dashboard.upcoming_sessions
                    if session.session_time.date() == datetime.utcnow().date()
                ],
            }

        elif dashboard_data.admin_dashboard:
            return {
                "role": "admin",
                "recent_bookings": dashboard_data.admin_dashboard.recent_bookings[:5],
                "recent_payments": dashboard_data.admin_dashboard.recent_payments[:5],
                "recent_reviews": dashboard_data.admin_dashboard.recent_reviews[:5],
            }
        else:
            raise HTTPException(status_code=500, detail="Invalid dashboard data")

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get quick actions: {str(e)}")


@router.get("/admin/dashboard", response_model=AdminDashboardSummary)
def get_admin_dashboard(
    *,
    session: SessionDep,
    current_user: AdminUser,
) -> Any:
    """
    Get admin dashboard summary with core metrics.

    Returns:
    - Total users
    - Total coaches
    - Number of bookings this month
    - Top 5 rated coaches (average rating)

    Admin access only.
    """
    try:
        admin_summary = dashboard_service.get_admin_dashboard_summary(session=session)
        return admin_summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get admin dashboard: {str(e)}")
