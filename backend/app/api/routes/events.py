import uuid
from typing import Any, Optional

from fastapi import APIRouter, HTTPException, Query

from app.api.deps import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CurrentUser, SessionDep
from app.models.event import EventStatus, RSVPStatus
from app.schemas.event import (
    EventCreate,
    EventPublic,
    EventSearchFilters,
    EventsPublic,
    EventStripeCheckoutRequest,
    EventStripeCheckoutResponse,
    EventUpdate,
    RSVPCreate,
    RSVPPublic,
    RSVPsPublic,
    RSVPUpdate,
)
from app.services.event_service import EventService
from app.services.event_stripe_service import EventStripeService

router = APIRouter()
event_service = EventService()
event_stripe_service = EventStripeService()


# Event management endpoints
@router.post("/events", response_model=EventPublic)
def create_event(
    *,
    session: SessionDep,
    current_user: CoachUser,
    event_in: EventCreate,
) -> Any:
    """
    Create a new event (coaches only).
    """
    event = event_service.create_event(
        session=session,
        host_id=current_user.id,
        event_in=event_in
    )
    return event


@router.get("/events", response_model=EventsPublic)
def search_events(
    *,
    session: SessionDep,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
    event_type: Optional[str] = Query(default=None, description="Filter by event type"),
    category: Optional[str] = Query(default=None, description="Filter by category"),
    location_id: Optional[uuid.UUID] = Query(default=None, description="Filter by location"),
    host_id: Optional[uuid.UUID] = Query(default=None, description="Filter by host"),
    min_price: Optional[float] = Query(default=None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(default=None, ge=0, description="Maximum price"),
    is_free: Optional[bool] = Query(default=None, description="Only free events"),
    has_spots_available: Optional[bool] = Query(default=None, description="Only events with spots"),
    tags: Optional[str] = Query(default=None, description="Search by tags"),
) -> Any:
    """
    Search and filter events.
    """
    filters = EventSearchFilters(
        event_type=event_type,
        category=category,
        location_id=location_id,
        host_id=host_id,
        min_price=min_price,
        max_price=max_price,
        is_free=is_free,
        has_spots_available=has_spots_available,
        tags=tags
    )

    events, count = event_service.search_events(
        session=session,
        filters=filters,
        skip=skip,
        limit=limit
    )

    return EventsPublic(data=events, count=count)


@router.get("/events/upcoming", response_model=EventsPublic)
def get_upcoming_events(
    *,
    session: SessionDep,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
    host_id: Optional[uuid.UUID] = Query(default=None, description="Filter by host"),
) -> Any:
    """
    Get upcoming events.
    """
    events = event_service.get_upcoming_events(
        session=session,
        skip=skip,
        limit=limit,
        host_id=host_id
    )

    return EventsPublic(data=events, count=len(events))


@router.get("/events/{event_id}", response_model=EventPublic)
def get_event(
    *,
    session: SessionDep,
    event_id: uuid.UUID,
) -> Any:
    """
    Get event by ID.
    """
    event = event_service.get_event_by_id(session=session, event_id=event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    return event


@router.put("/events/{event_id}", response_model=EventPublic)
def update_event(
    *,
    session: SessionDep,
    current_user: CoachUser,
    event_id: uuid.UUID,
    event_update: EventUpdate,
) -> Any:
    """
    Update an event (host only).
    """
    event = event_service.update_event(
        session=session,
        event_id=event_id,
        host_id=current_user.id,
        event_update=event_update
    )

    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    return event


@router.delete("/events/{event_id}")
def delete_event(
    *,
    session: SessionDep,
    current_user: CoachUser,
    event_id: uuid.UUID,
) -> Any:
    """
    Delete an event (host only).
    """
    success = event_service.delete_event(
        session=session,
        event_id=event_id,
        host_id=current_user.id
    )

    if not success:
        raise HTTPException(status_code=404, detail="Event not found")

    return {"message": "Event deleted successfully"}


# Coach event management
@router.get("/my-events", response_model=EventsPublic)
def get_my_events(
    *,
    session: SessionDep,
    current_user: CoachUser,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
    status: Optional[EventStatus] = Query(default=None, description="Filter by status"),
) -> Any:
    """
    Get events hosted by the current coach.
    """
    events = event_service.get_events_by_host(
        session=session,
        host_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status
    )

    return EventsPublic(data=events, count=len(events))


# RSVP endpoints
@router.post("/events/{event_id}/rsvp", response_model=RSVPPublic)
def create_rsvp(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    event_id: uuid.UUID,
    rsvp_in: RSVPCreate,
) -> Any:
    """
    RSVP for an event.
    """
    # Ensure the event_id matches
    rsvp_in.event_id = event_id

    rsvp = event_service.create_rsvp(
        session=session,
        user_id=current_user.id,
        rsvp_in=rsvp_in
    )

    return rsvp


@router.get("/events/{event_id}/rsvps", response_model=RSVPsPublic)
def get_event_rsvps(
    *,
    session: SessionDep,
    current_user: CoachUser,
    event_id: uuid.UUID,
    skip: int = 0,
    limit: int = Query(default=50, le=200),
    status: Optional[RSVPStatus] = Query(default=None, description="Filter by RSVP status"),
) -> Any:
    """
    Get RSVPs for an event (host only).
    """
    rsvps = event_service.get_event_rsvps(
        session=session,
        event_id=event_id,
        host_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status
    )

    return RSVPsPublic(data=rsvps, count=len(rsvps))


@router.get("/my-rsvps", response_model=RSVPsPublic)
def get_my_rsvps(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
) -> Any:
    """
    Get current user's RSVPs.
    """
    rsvps = event_service.get_user_rsvps(
        session=session,
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )

    return RSVPsPublic(data=rsvps, count=len(rsvps))


@router.put("/rsvps/{rsvp_id}", response_model=RSVPPublic)
def update_rsvp(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    rsvp_id: uuid.UUID,
    rsvp_update: RSVPUpdate,
) -> Any:
    """
    Update an RSVP.
    """
    # For now, only allow cancellation
    if rsvp_update.status and rsvp_update.status != RSVPStatus.CANCELLED:
        raise HTTPException(status_code=400, detail="Only cancellation is allowed")

    rsvp = event_service.cancel_rsvp(
        session=session,
        rsvp_id=rsvp_id,
        user_id=current_user.id
    )

    if not rsvp:
        raise HTTPException(status_code=404, detail="RSVP not found")

    return rsvp


# Payment endpoints
@router.post("/rsvps/{rsvp_id}/checkout", response_model=EventStripeCheckoutResponse)
def create_event_checkout(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    rsvp_id: uuid.UUID,
    checkout_request: EventStripeCheckoutRequest,
) -> Any:
    """
    Create Stripe checkout session for event payment.
    """
    # Ensure the RSVP ID matches
    checkout_request.rsvp_id = rsvp_id

    checkout_response = event_stripe_service.create_checkout_session(
        session=session,
        user_id=current_user.id,
        checkout_request=checkout_request
    )

    return checkout_response


@router.get("/rsvps/{rsvp_id}/payment-status")
def get_payment_status(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    rsvp_id: uuid.UUID,
) -> Any:
    """
    Get payment status for an RSVP.
    """
    payment_status = event_stripe_service.get_payment_status(
        session=session,
        rsvp_id=rsvp_id
    )

    return payment_status


# Admin endpoints
@router.post("/events/{event_id}/publish", response_model=EventPublic)
def publish_event(
    *,
    session: SessionDep,
    current_user: AdminUser,
    event_id: uuid.UUID,
) -> Any:
    """
    Publish an event (admin only).
    """
    from app.models.event import Event

    event = session.get(Event, event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    event.status = EventStatus.PUBLISHED.value
    session.commit()
    session.refresh(event)

    return event
