import uuid
from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session

from app.api.deps import CurrentUser, get_db
from app.models.booking import BookingStatus
from app.models.user import User, UserRole
from app.schemas.booking import (
    BookingCreate,
    BookingPublic,
    BookingPublicWithCoach,
    BookingPublicWithUser,
    BookingsPublicWithCoach,
    BookingsPublicWithUser,
    BookingStatusUpdate,
    BookingUpdate,
)
from app.schemas.common import Message
from app.services.booking_service import BookingService
from app.services.coach_service import CoachService

router = APIRouter()
booking_service = BookingService()
coach_service = CoachService()


@router.get("/test", response_model=dict)
def test_booking_endpoint() -> dict:
    """Test endpoint to verify booking routes are working."""
    return {"message": "Booking endpoints are working", "status": "ok"}


@router.post("/", response_model=BookingPublic)
def create_booking(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
    booking_in: BookingCreate,
) -> Any:
    """
    Create a new booking.
    """
    booking = booking_service.create_booking(
        session=session,
        user_id=current_user.id,
        booking_data=booking_in
    )
    return booking


@router.get("/my-bookings", response_model=dict)
def get_my_bookings(
    current_user: CurrentUser,
) -> dict:
    """
    Get current user's bookings.
    """
    return {"message": "My bookings endpoint", "user_id": str(current_user.id)}


@router.get("/coach-bookings", response_model=BookingsPublicWithUser)
def get_coach_bookings(
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
    status: Optional[BookingStatus] = Query(None, description="Filter by booking status"),
    upcoming_only: bool = Query(False, description="Show only upcoming bookings"),
    skip: int = Query(0, ge=0, description="Number of bookings to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of bookings to return"),
) -> Any:
    """
    Get bookings for the current user's coach profile.
    Only accessible by users with coach role.
    """
    if current_user.role != UserRole.COACH.value:
        raise HTTPException(status_code=403, detail="Only coaches can access this endpoint")

    # Get the coach profile for the current user
    coach_profile = coach_service.get_coach_by_user_id(session=session, user_id=current_user.id)
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    bookings, count = booking_service.get_coach_bookings(
        session=session,
        coach_id=coach_profile.id,
        status=status,
        upcoming_only=upcoming_only,
        skip=skip,
        limit=limit
    )
    return BookingsPublicWithUser(data=bookings, count=count)


@router.get("/{booking_id}", response_model=BookingPublic)
def get_booking(
    booking_id: uuid.UUID,
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
) -> Any:
    """
    Get a specific booking by ID.
    """
    booking = booking_service.get_booking_by_id(
        session=session,
        booking_id=booking_id,
        user_id=current_user.id
    )
    return booking


@router.put("/{booking_id}", response_model=BookingPublic)
def update_booking(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
    booking_id: uuid.UUID,
    booking_in: BookingUpdate,
) -> Any:
    """
    Update a booking.
    Only the user who created the booking can update it.
    """
    booking = booking_service.update_booking(
        session=session,
        booking_id=booking_id,
        user_id=current_user.id,
        booking_data=booking_in
    )
    return booking


@router.patch("/{booking_id}/status", response_model=BookingPublic)
def update_booking_status(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
    booking_id: uuid.UUID,
    status_update: BookingStatusUpdate,
) -> Any:
    """
    Update booking status.
    Only the coach can update the booking status.
    """
    booking = booking_service.update_booking_status(
        session=session,
        booking_id=booking_id,
        coach_user_id=current_user.id,
        status_data=status_update
    )
    return booking


@router.delete("/{booking_id}", response_model=Message)
def cancel_booking(
    booking_id: uuid.UUID,
    session: Session = Depends(get_db),
    current_user: User = Depends(CurrentUser),
) -> Any:
    """
    Cancel a booking.
    Both the user who created the booking and the coach can cancel it.
    """
    booking_service.cancel_booking(
        session=session,
        booking_id=booking_id,
        user_id=current_user.id
    )
    return Message(message="Booking cancelled successfully")
