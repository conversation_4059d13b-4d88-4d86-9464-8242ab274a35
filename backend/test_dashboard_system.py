#!/usr/bin/env python3
"""
Test script to verify dashboard system is working correctly.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.user import User, UserRole
from app.schemas.dashboard import (
    DashboardResponse,
    UserDashboard,
    CoachDashboard,
    AdminDashboard,
    DashboardStats,
    CoachDashboardStats
)


def test_dashboard_schemas():
    """Test that dashboard schemas work correctly."""
    try:
        # Test DashboardStats
        user_stats = DashboardStats(
            total_bookings=5,
            confirmed_bookings=3,
            completed_bookings=2,
            cancelled_bookings=0,
            total_spent=450.0,
            total_reviews=2,
            average_rating_given=4.5
        )
        print("✅ DashboardStats schema working")

        # Test CoachDashboardStats
        coach_stats = CoachDashboardStats(
            total_sessions=15,
            confirmed_sessions=12,
            completed_sessions=10,
            cancelled_sessions=1,
            total_earnings=1200.0,
            total_reviews_received=8,
            average_rating_received=4.7,
            featured_testimonials=3,
            this_month_sessions=5,
            this_month_earnings=400.0
        )
        print("✅ CoachDashboardStats schema working")

        # Test UserDashboard
        user_dashboard = UserDashboard(
            user=None,  # Would be populated with actual user data
            upcoming_sessions=[],
            recent_bookings=[],
            reviews_left=[],
            recent_payments=[],
            stats=user_stats,
            favorite_coaches=[],
            pending_payments=[]
        )
        print("✅ UserDashboard schema working")

        # Test CoachDashboard
        coach_dashboard = CoachDashboard(
            user=None,  # Would be populated with actual user data
            coach_profile=None,  # Would be populated with actual coach data
            upcoming_sessions=[],
            recent_sessions=[],
            reviews_received=[],
            recent_earnings=[],
            stats=coach_stats,
            pending_sessions=[],
            featured_testimonials=[]
        )
        print("✅ CoachDashboard schema working")

        # Test AdminDashboard
        admin_dashboard = AdminDashboard(
            total_users=150,
            total_coaches=25,
            total_bookings=500,
            total_payments=15000.0,
            total_reviews=200,
            recent_bookings=[],
            recent_payments=[],
            recent_reviews=[],
            this_month_bookings=50,
            this_month_revenue=2000.0,
            this_month_new_users=15,
            this_month_new_coaches=3
        )
        print("✅ AdminDashboard schema working")

        # Test DashboardResponse
        dashboard_response = DashboardResponse(
            user_role="user",
            user_dashboard=user_dashboard
        )
        print("✅ DashboardResponse schema working")

        return True
    except Exception as e:
        print(f"❌ Failed to test dashboard schemas: {e}")
        return False


def test_dashboard_service_import():
    """Test that DashboardService can be imported."""
    try:
        from app.services.dashboard_service import DashboardService
        dashboard_service = DashboardService()

        # Check if main method exists
        if hasattr(dashboard_service, 'get_dashboard_for_user'):
            print("✅ DashboardService.get_dashboard_for_user method available")
        else:
            print("❌ DashboardService.get_dashboard_for_user method missing")
            return False

        # Check if helper methods exist
        helper_methods = [
            '_get_user_dashboard',
            '_get_coach_dashboard',
            '_get_admin_dashboard',
            '_get_user_stats',
            '_get_coach_stats'
        ]

        for method in helper_methods:
            if hasattr(dashboard_service, method):
                print(f"✅ DashboardService.{method} method available")
            else:
                print(f"❌ DashboardService.{method} method missing")
                return False

        print("✅ DashboardService imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import DashboardService: {e}")
        return False


def test_dashboard_routes_import():
    """Test that dashboard routes can be imported."""
    try:
        from app.api.routes.dashboard import router

        # Check if router has the expected routes
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/me/dashboard",
            "/dashboard/user/{user_id}",
            "/dashboard/stats",
            "/dashboard/quick-actions"
        ]

        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✅ Dashboard route {expected_route} available")
            else:
                print(f"❌ Dashboard route {expected_route} missing")
                return False

        print("✅ Dashboard routes imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import dashboard routes: {e}")
        return False


def test_api_endpoints():
    """Test that dashboard API endpoints are accessible."""
    import requests

    base_url = "http://localhost:8003/api/v1"

    try:
        # Test that endpoints exist (should return 401 for unauthenticated requests)
        endpoints = [
            "/me/dashboard",
            "/dashboard/stats",
            "/dashboard/quick-actions"
        ]

        for endpoint in endpoints:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 401:  # Unauthorized (expected without token)
                print(f"✅ Dashboard endpoint {endpoint} accessible (requires auth)")
            elif response.status_code == 422:  # Validation error (also acceptable)
                print(f"✅ Dashboard endpoint {endpoint} accessible (validation required)")
            else:
                print(f"⚠️  Dashboard endpoint {endpoint} returned {response.status_code}")

        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   Make sure the server is running on http://localhost:8003")
        return False


def test_openapi_documentation():
    """Test that dashboard endpoints are documented in OpenAPI."""
    import requests

    try:
        response = requests.get("http://localhost:8003/api/v1/openapi.json", timeout=5)
        if response.status_code == 200:
            openapi_spec = response.json()

            # Check if dashboard endpoints are in the spec
            paths = openapi_spec.get("paths", {})
            dashboard_endpoints = [
                "/api/v1/me/dashboard",
                "/api/v1/dashboard/user/{user_id}",
                "/api/v1/dashboard/stats",
                "/api/v1/dashboard/quick-actions"
            ]

            found_endpoints = 0
            for endpoint in dashboard_endpoints:
                if endpoint in paths:
                    print(f"✅ Dashboard endpoint {endpoint} documented in OpenAPI")
                    found_endpoints += 1
                else:
                    print(f"❌ Dashboard endpoint {endpoint} missing from OpenAPI")

            if found_endpoints == len(dashboard_endpoints):
                print("✅ All dashboard endpoints documented in OpenAPI")
                return True
            else:
                print(f"⚠️  Only {found_endpoints}/{len(dashboard_endpoints)} endpoints documented")
                return False
        else:
            print(f"❌ Failed to get OpenAPI spec: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ OpenAPI documentation test failed: {e}")
        return False


def main():
    """Run all dashboard system tests."""
    print("🧪 Testing Dashboard System")
    print("=" * 60)

    tests = [
        test_dashboard_schemas,
        test_dashboard_service_import,
        test_dashboard_routes_import,
        test_api_endpoints,
        test_openapi_documentation,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 40)

    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All dashboard system tests passed!")
        print("\n✨ Your dashboard system is working perfectly!")
        print("\n📋 Available Dashboard Features:")
        print("   ✅ Role-specific dashboards (User, Coach, Admin)")
        print("   ✅ Unified /me/dashboard endpoint")
        print("   ✅ Comprehensive statistics and analytics")
        print("   ✅ Quick actions and pending items")
        print("   ✅ Recent activity and upcoming events")
        print("   ✅ Admin user dashboard viewing")
        print("   ✅ Lightweight stats endpoint")
        print("\n🚀 Ready for frontend integration!")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
