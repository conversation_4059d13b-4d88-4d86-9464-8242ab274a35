#!/usr/bin/env python3
"""
Test script to verify enhanced review/testimonial system is working correctly.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.review import Review
from app.schemas.review import (
    ReviewCreate, 
    ReviewUpdate, 
    TestimonialPublic, 
    FeaturedTestimonialsResponse
)


def test_enhanced_review_model():
    """Test that enhanced Review model has testimonial fields."""
    try:
        # Test Review model with new testimonial fields
        review = Review(
            coach_profile_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            rating=5,
            comment="Amazing spiritual guidance! Life-changing experience.",
            is_featured=True,
            is_public=True,
            session_type="Mindset Transformation"
        )
        
        print("✅ Enhanced Review model created successfully")
        print(f"   Review ID: {review.id}")
        print(f"   Rating: {review.rating}")
        print(f"   Is Featured: {review.is_featured}")
        print(f"   Is Public: {review.is_public}")
        print(f"   Session Type: {review.session_type}")
        return True
    except Exception as e:
        print(f"❌ Failed to create enhanced Review model: {e}")
        return False


def test_testimonial_schemas():
    """Test that testimonial schemas work correctly."""
    try:
        # Test ReviewCreate with new fields
        review_create = ReviewCreate(
            coach_profile_id=uuid.uuid4(),
            rating=5,
            comment="Incredible spiritual awakening session!",
            session_type="Energy Healing"
        )
        print("✅ ReviewCreate schema with testimonial fields working")
        
        # Test ReviewUpdate
        review_update = ReviewUpdate(
            rating=4,
            is_featured=True,
            is_public=True
        )
        print("✅ ReviewUpdate schema working")
        
        # Test TestimonialPublic (alias for ReviewPublic)
        print("✅ TestimonialPublic schema available")
        
        # Test FeaturedTestimonialsResponse
        featured_response = FeaturedTestimonialsResponse(
            testimonials=[],
            total_count=0,
            average_rating=None
        )
        print("✅ FeaturedTestimonialsResponse schema working")
        
        return True
    except Exception as e:
        print(f"❌ Failed to test testimonial schemas: {e}")
        return False


def test_review_service_import():
    """Test that enhanced ReviewService can be imported."""
    try:
        from app.services.review_service import ReviewService
        review_service = ReviewService()
        
        # Check if new methods exist
        methods = [
            'get_testimonials_for_coach',
            'count_testimonials_for_coach', 
            'get_featured_testimonials',
            'count_all_testimonials',
            'get_overall_average_rating',
            'feature_review'
        ]
        
        for method in methods:
            if hasattr(review_service, method):
                print(f"✅ ReviewService.{method} method available")
            else:
                print(f"❌ ReviewService.{method} method missing")
                return False
        
        print("✅ Enhanced ReviewService imported and verified successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import enhanced ReviewService: {e}")
        return False


def test_api_endpoints():
    """Test that API endpoints are accessible."""
    import requests
    
    base_url = "http://localhost:8003/api/v1"
    
    endpoints = [
        "/coaches/testimonials/featured",
        "/coaches/coaches",  # Get a coach ID first
    ]
    
    try:
        # Test featured testimonials endpoint
        response = requests.get(f"{base_url}/coaches/testimonials/featured", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "testimonials" in data and "total_count" in data:
                print("✅ Featured testimonials endpoint working")
            else:
                print("❌ Featured testimonials endpoint response format incorrect")
                return False
        else:
            print(f"❌ Featured testimonials endpoint failed: {response.status_code}")
            return False
        
        # Get coaches to test coach-specific endpoints
        response = requests.get(f"{base_url}/coaches/coaches", timeout=5)
        if response.status_code == 200:
            coaches = response.json()
            if coaches.get("data"):
                coach_id = coaches["data"][0]["id"]
                
                # Test coach testimonials
                response = requests.get(f"{base_url}/coaches/coaches/{coach_id}/testimonials", timeout=5)
                if response.status_code == 200:
                    print("✅ Coach testimonials endpoint working")
                else:
                    print(f"❌ Coach testimonials endpoint failed: {response.status_code}")
                    return False
                
                # Test enhanced reviews endpoint
                response = requests.get(f"{base_url}/coaches/coaches/{coach_id}/reviews", timeout=5)
                if response.status_code == 200:
                    print("✅ Enhanced reviews endpoint working")
                else:
                    print(f"❌ Enhanced reviews endpoint failed: {response.status_code}")
                    return False
            else:
                print("⚠️  No coaches found for testing coach-specific endpoints")
        
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   Make sure the server is running on http://localhost:8003")
        return False


def main():
    """Run all testimonial system tests."""
    print("🧪 Testing Enhanced Review/Testimonial System")
    print("=" * 60)
    
    tests = [
        test_enhanced_review_model,
        test_testimonial_schemas,
        test_review_service_import,
        test_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All testimonial system tests passed!")
        print("\n✨ Your enhanced review/testimonial system is working perfectly!")
        print("\n📋 Available Features:")
        print("   ✅ Enhanced Review model with testimonial fields")
        print("   ✅ Featured testimonials for marketing")
        print("   ✅ Coach-specific testimonials (4+ stars)")
        print("   ✅ Public/private review controls")
        print("   ✅ Session type tracking")
        print("   ✅ Admin testimonial featuring")
        print("   ✅ Platform-wide testimonial analytics")
        print("\n🚀 Ready for frontend integration!")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
