# Deployment Guide

## Overview

This guide covers deployment strategies for the Hi-Vi Spiritual Coaching Platform backend, including containerization, cloud deployment, and CI/CD pipeline setup.

## Deployment Architecture

### Production Environment
- **Application**: FastAPI backend running in Docker containers
- **Database**: PostgreSQL (managed service recommended)
- **Load Balancer**: AWS Application Load Balancer or similar
- **CDN**: CloudFront for static assets
- **Monitoring**: CloudWatch, Sentry for error tracking

### Staging Environment
- Mirror of production with reduced resources
- Used for testing before production deployment
- Separate database instance

## Docker Deployment

### Building the Docker Image

The backend includes a multi-stage Dockerfile optimized for production:

```dockerfile
# Build stage
FROM python:3.11-slim as builder
WORKDIR /app
COPY pyproject.toml uv.lock ./
RUN pip install uv && uv sync --frozen

# Production stage
FROM python:3.11-slim
WORKDIR /app
COPY --from=builder /app/.venv /app/.venv
COPY ./app /app/app
ENV PATH="/app/.venv/bin:$PATH"
CMD ["fastapi", "run", "app/main.py", "--host", "0.0.0.0", "--port", "8000"]
```

### Build Commands

```bash
# Build the image
docker build -t hivi-backend:latest .

# Run locally
docker run -p 8000:8000 --env-file .env hivi-backend:latest

# Push to registry
docker tag hivi-backend:latest your-registry/hivi-backend:latest
docker push your-registry/hivi-backend:latest
```

## AWS Deployment

### ECS Deployment

#### Task Definition

```json
{
  "family": "hivi-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "hivi-backend",
      "image": "your-registry/hivi-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "POSTGRES_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:hivi/postgres-password"
        },
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:hivi/secret-key"
        },
        {
          "name": "STRIPE_SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:hivi/stripe-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/hivi-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/utils/health-check/ || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

#### Service Configuration

```json
{
  "serviceName": "hivi-backend",
  "cluster": "hivi-cluster",
  "taskDefinition": "hivi-backend:1",
  "desiredCount": 2,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": ["subnet-12345", "subnet-67890"],
      "securityGroups": ["sg-backend"],
      "assignPublicIp": "DISABLED"
    }
  },
  "loadBalancers": [
    {
      "targetGroupArn": "arn:aws:elasticloadbalancing:region:account:targetgroup/hivi-backend",
      "containerName": "hivi-backend",
      "containerPort": 8000
    }
  ],
  "healthCheckGracePeriodSeconds": 300
}
```

### RDS Database Setup

#### PostgreSQL Configuration

```sql
-- Create database
CREATE DATABASE hivi_production;

-- Create application user
CREATE USER hivi_app WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE hivi_production TO hivi_app;

-- Configure connection limits
ALTER USER hivi_app CONNECTION LIMIT 20;
```

#### Parameter Group Settings

```
shared_preload_libraries = 'pg_stat_statements'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

## Environment Configuration

### Production Environment Variables

```bash
# Application
ENVIRONMENT=production
PROJECT_NAME="Hi-Vi Spiritual Coaching Platform"
API_V1_STR="/api/v1"
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# Database
POSTGRES_SERVER=your-rds-endpoint.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_USER=hivi_app
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=hivi_production

# CORS
BACKEND_CORS_ORIGINS=["https://dashboard.hi-vi.com", "https://hi-vi.com"]
FRONTEND_HOST=https://dashboard.hi-vi.com

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_SUCCESS_URL=https://dashboard.hi-vi.com/booking/success
STRIPE_CANCEL_URL=https://dashboard.hi-vi.com/booking/cancel

# Email
SMTP_HOST=smtp.amazonaws.com
SMTP_PORT=587
SMTP_USER=your-ses-user
SMTP_PASSWORD=your-ses-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="Hi-Vi Platform"

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
```

### Secrets Management

Use AWS Secrets Manager for sensitive configuration:

```bash
# Create secrets
aws secretsmanager create-secret \
  --name "hivi/postgres-password" \
  --description "PostgreSQL password for Hi-Vi backend" \
  --secret-string "your-secure-password"

aws secretsmanager create-secret \
  --name "hivi/secret-key" \
  --description "JWT secret key for Hi-Vi backend" \
  --secret-string "your-jwt-secret-key"

aws secretsmanager create-secret \
  --name "hivi/stripe-secret" \
  --description "Stripe secret key for Hi-Vi backend" \
  --secret-string "sk_live_your_stripe_key"
```

## CI/CD Pipeline

### GitHub Actions Workflow

```yaml
name: Deploy Backend

on:
  push:
    branches: [main]
    paths: ['backend/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd backend
          pip install uv
          uv sync
      - name: Run tests
        run: |
          cd backend
          uv run pytest
      - name: Run linting
        run: |
          cd backend
          uv run ruff check .
          uv run mypy .

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: hivi-backend
          IMAGE_TAG: ${{ github.sha }}
        run: |
          cd backend
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

      - name: Update ECS service
        run: |
          aws ecs update-service \
            --cluster hivi-cluster \
            --service hivi-backend \
            --force-new-deployment
```

## Database Migrations

### Production Migration Strategy

```bash
# Run migrations in production
docker run --rm \
  --env-file .env.production \
  your-registry/hivi-backend:latest \
  alembic upgrade head

# Or via ECS task
aws ecs run-task \
  --cluster hivi-cluster \
  --task-definition hivi-migration \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-backend]}"
```

### Migration Task Definition

```json
{
  "family": "hivi-migration",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "containerDefinitions": [
    {
      "name": "migration",
      "image": "your-registry/hivi-backend:latest",
      "command": ["alembic", "upgrade", "head"],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "POSTGRES_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:hivi/postgres-password"
        }
      ]
    }
  ]
}
```

## Monitoring and Logging

### Health Checks

The application includes health check endpoints:

```python
@router.get("/health-check/")
def health_check():
    return {"status": "healthy"}

@router.get("/health-check/db/")
def health_check_db(session: SessionDep):
    try:
        session.exec(text("SELECT 1"))
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        raise HTTPException(status_code=503, detail="Database connection failed")
```

### CloudWatch Metrics

Key metrics to monitor:
- Request count and latency
- Error rates (4xx, 5xx)
- Database connection pool usage
- Memory and CPU utilization

### Sentry Integration

Error tracking is configured in `main.py`:

```python
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(
        dsn=str(settings.SENTRY_DSN),
        integrations=[FastApiIntegration()],
        traces_sample_rate=0.1,
        environment=settings.ENVIRONMENT,
    )
```

## Security Considerations

### Network Security
- Use VPC with private subnets for application
- Security groups with minimal required access
- WAF for additional protection

### Data Security
- Encrypt data at rest (RDS encryption)
- Encrypt data in transit (TLS/SSL)
- Regular security updates for base images

### Access Control
- IAM roles with least privilege principle
- Secrets stored in AWS Secrets Manager
- Regular credential rotation

## Scaling Considerations

### Horizontal Scaling
- ECS service auto-scaling based on CPU/memory
- Application Load Balancer for traffic distribution
- Database read replicas for read-heavy workloads

### Performance Optimization
- Connection pooling for database
- Caching layer (Redis/ElastiCache)
- CDN for static assets

## Backup and Disaster Recovery

### Database Backups
- Automated daily backups with 30-day retention
- Point-in-time recovery capability
- Cross-region backup replication

### Application Recovery
- Multi-AZ deployment for high availability
- Infrastructure as Code for quick recovery
- Documented recovery procedures

## Cost Optimization

### Resource Optimization
- Right-sizing ECS tasks based on usage
- Spot instances for non-critical workloads
- Reserved instances for predictable workloads

### Monitoring Costs
- AWS Cost Explorer for cost analysis
- Budget alerts for unexpected spending
- Regular review of resource utilization
