# Backend Architecture Documentation

## Overview

The Hi-Vi Spiritual Coaching Platform backend is built using FastAPI and follows a layered architecture pattern with clear separation of concerns. This document provides a detailed overview of the system architecture, design patterns, and component interactions.

## Architecture Layers

### 1. API Layer (`app/api/`)

The API layer handles HTTP requests and responses, providing RESTful endpoints for the frontend application.

**Key Components:**
- **Routes** (`app/api/routes/`) - Individual route modules for different domains
- **Dependencies** (`app/api/deps.py`) - Shared dependencies like authentication and database sessions
- **Main Router** (`app/api/main.py`) - Central router configuration

**Route Modules:**
- `login.py` - Authentication endpoints
- `users.py` - User management endpoints
- `coaches.py` - Coach profile management
- `bookings.py` - Session booking endpoints
- `events.py` - Event management endpoints
- `payments.py` - Payment processing endpoints
- `dashboard.py` - Dashboard data endpoints
- `coach_availability.py` - Coach availability management

### 2. Service Layer (`app/services/`)

The service layer contains business logic and orchestrates operations between different components.

**Key Services:**
- `CoachService` - Coach profile and search operations
- `BookingService` - Session booking logic
- `EventService` - Event management and RSVP handling
- `StripeService` - Payment processing integration
- `DashboardService` - Dashboard data aggregation
- `ReviewService` - Review and testimonial management

**Design Patterns:**
- **Service Pattern** - Encapsulates business logic
- **Dependency Injection** - Services depend on repositories, not direct database access

### 3. Repository Layer (`app/repositories/`)

The repository layer provides data access abstraction and database operations.

**Key Repositories:**
- `BaseRepository` - Common CRUD operations
- `CoachRepository` - Coach-specific database operations
- `BookingRepository` - Booking data access
- `EventRepository` - Event data management
- `PaymentRepository` - Payment record management

**Benefits:**
- **Separation of Concerns** - Business logic separated from data access
- **Testability** - Easy to mock for unit testing
- **Consistency** - Standardized data access patterns

### 4. Model Layer (`app/models/`)

SQLModel-based database models that define the data structure and relationships.

**Core Models:**
- `User` - User accounts with role-based access
- `CoachProfile` - Extended coach information
- `Booking` - Session bookings
- `Event` - Group events and circles
- `EventRSVP` - Event registrations
- `Payment` - Payment records
- `Review` - Coach reviews and testimonials

### 5. Schema Layer (`app/schemas/`)

Pydantic models for API request/response serialization and validation.

**Schema Types:**
- **Create Schemas** - For creating new resources
- **Update Schemas** - For updating existing resources
- **Response Schemas** - For API responses
- **Filter Schemas** - For search and filtering operations

## Core Components

### Authentication & Authorization

**JWT-based Authentication:**
- Access tokens with configurable expiration
- Role-based access control (user, coach, admin)
- Secure password hashing with bcrypt

**Security Features:**
- CORS configuration for cross-origin requests
- Input validation and sanitization
- SQL injection prevention through SQLModel

### Database Integration

**PostgreSQL with SQLModel:**
- Type-safe database operations
- Automatic schema generation
- Migration support with Alembic

**Connection Management:**
- Connection pooling for performance
- Session management with dependency injection
- Transaction support for data consistency

### Payment Processing

**Stripe Integration:**
- Secure payment processing
- Webhook handling for payment events
- Support for both one-time and recurring payments
- Automatic booking confirmation on successful payment

### Event System

**Comprehensive Event Management:**
- Group events and spiritual circles
- RSVP functionality with capacity management
- Waitlist support when events are full
- Multiple event types (online, in-person, hybrid)

## Data Flow

### Typical Request Flow

1. **HTTP Request** arrives at FastAPI route
2. **Authentication** middleware validates JWT token
3. **Route Handler** extracts and validates request data
4. **Service Layer** processes business logic
5. **Repository Layer** performs database operations
6. **Response** is serialized and returned

### Example: Coach Search Flow

```
GET /api/v1/coaches?category=meditation&location=online
    ↓
coaches.py route handler
    ↓
CoachService.search_coaches()
    ↓
CoachRepository.search_with_filters()
    ↓
PostgreSQL query execution
    ↓
Response serialization
    ↓
JSON response to client
```

## Design Principles

### 1. Separation of Concerns
Each layer has a specific responsibility and doesn't directly access other layers' internals.

### 2. Dependency Inversion
Higher-level modules don't depend on lower-level modules; both depend on abstractions.

### 3. Single Responsibility
Each class and module has a single, well-defined purpose.

### 4. Open/Closed Principle
Components are open for extension but closed for modification.

### 5. DRY (Don't Repeat Yourself)
Common functionality is abstracted into reusable components.

## Configuration Management

**Environment-based Configuration:**
- Development, staging, and production environments
- Environment variables for sensitive data
- Pydantic Settings for type-safe configuration

**Key Configuration Areas:**
- Database connection settings
- JWT token configuration
- Stripe API keys and webhook secrets
- CORS origins and security settings
- Email service configuration

## Error Handling

**Structured Error Responses:**
- HTTP status codes following REST conventions
- Detailed error messages for development
- Generic error messages for production
- Validation error details for client-side handling

**Exception Handling:**
- Global exception handlers for common errors
- Custom exceptions for business logic errors
- Proper logging for debugging and monitoring

## Performance Considerations

**Database Optimization:**
- Proper indexing on frequently queried fields
- Connection pooling for concurrent requests
- Query optimization with SQLModel

**Caching Strategy:**
- Response caching for frequently accessed data
- Database query result caching
- Static asset caching

**Scalability:**
- Stateless application design
- Horizontal scaling capability
- Load balancer compatibility

## Security Measures

**Data Protection:**
- Password hashing with bcrypt
- JWT token-based authentication
- Input validation and sanitization
- SQL injection prevention

**API Security:**
- Rate limiting (configurable)
- CORS policy enforcement
- HTTPS enforcement in production
- Secure headers configuration

## Monitoring & Observability

**Logging:**
- Structured logging with appropriate levels
- Request/response logging for debugging
- Error tracking and alerting

**Health Checks:**
- Database connectivity checks
- External service availability checks
- Application health endpoints

**Metrics:**
- Response time monitoring
- Error rate tracking
- Database performance metrics
