# API Reference Documentation

## Overview

The Hi-Vi Spiritual Coaching Platform provides a comprehensive REST API for managing users, coaches, bookings, events, and payments. All endpoints follow RESTful conventions and return JSON responses.

## Base URL

- **Development**: `http://localhost:8000/api/v1`
- **Production**: `https://api.hi-vi.com/api/v1`

## Authentication

Most endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /login/access-token
Login with email and password to receive an access token.

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

#### POST /login/test-token
Test if the current token is valid.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "email": "<EMAIL>",
  "id": "uuid-string",
  "is_active": true,
  "role": "user"
}
```

## User Management

### GET /users/me
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": "uuid-string",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "role": "user",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### PUT /users/me
Update current user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "full_name": "John Smith",
  "email": "<EMAIL>"
}
```

### POST /users/signup
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure-password",
  "full_name": "New User",
  "role": "user"
}
```

## Coach Management

### GET /coaches
Search and browse spiritual coaches with filters.

**Query Parameters:**
- `category` (optional): Filter by coach category
- `location` (optional): Filter by location
- `min_rating` (optional): Minimum average rating
- `max_rate` (optional): Maximum hourly rate
- `skip` (optional): Number of records to skip (pagination)
- `limit` (optional): Maximum number of records to return

**Example Request:**
```
GET /coaches?category=meditation&location=online&min_rating=4.0&limit=10
```

**Response:**
```json
{
  "coaches": [
    {
      "id": "uuid-string",
      "user_id": "uuid-string",
      "bio": "Experienced meditation teacher...",
      "experience_years": 5,
      "hourly_rate": 75.0,
      "average_rating": 4.8,
      "total_reviews": 24,
      "category": {
        "id": "uuid-string",
        "name": "Mindset & Emotional Growth Coaches"
      },
      "location": {
        "id": "uuid-string",
        "name": "Online",
        "type": "virtual"
      },
      "user": {
        "full_name": "Sarah Johnson",
        "email": "<EMAIL>"
      }
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 10
}
```

### GET /coaches/{coach_id}
Get detailed information about a specific coach.

**Response:**
```json
{
  "id": "uuid-string",
  "user_id": "uuid-string",
  "bio": "Detailed coach biography...",
  "experience_years": 5,
  "hourly_rate": 75.0,
  "average_rating": 4.8,
  "total_reviews": 24,
  "profile_image_url": "https://example.com/image.jpg",
  "is_available": true,
  "category": {
    "name": "Mindset & Emotional Growth Coaches"
  },
  "location": {
    "name": "Online",
    "type": "virtual"
  },
  "user": {
    "full_name": "Sarah Johnson"
  }
}
```

### POST /coaches/profile
Create a coach profile (requires coach role).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "bio": "I am a certified spiritual coach...",
  "experience_years": 3,
  "hourly_rate": 60.0,
  "category_id": "uuid-string",
  "location_id": "uuid-string",
  "profile_image_url": "https://example.com/profile.jpg"
}
```

### PUT /coaches/profile
Update coach profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "bio": "Updated biography...",
  "hourly_rate": 65.0,
  "is_available": true
}
```

## Booking Management

### POST /bookings
Create a new session booking.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "coach_id": "uuid-string",
  "session_time": "2024-02-15T14:00:00Z",
  "duration": 60,
  "session_type": "Life Coaching",
  "notes": "Looking forward to our session"
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "coach_id": "uuid-string",
  "session_time": "2024-02-15T14:00:00Z",
  "duration": 60,
  "status": "pending",
  "session_type": "Life Coaching",
  "notes": "Looking forward to our session",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### GET /bookings/my-bookings
Get current user's bookings.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `status` (optional): Filter by booking status
- `skip` (optional): Pagination offset
- `limit` (optional): Maximum results

**Response:**
```json
{
  "bookings": [
    {
      "id": "uuid-string",
      "session_time": "2024-02-15T14:00:00Z",
      "duration": 60,
      "status": "confirmed",
      "coach": {
        "user": {
          "full_name": "Sarah Johnson"
        }
      },
      "payment": {
        "amount": 75.0,
        "status": "completed"
      }
    }
  ],
  "total": 1
}
```

### PUT /bookings/{booking_id}/status
Update booking status (coach only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "status": "confirmed",
  "meeting_link": "https://zoom.us/j/123456789"
}
```

## Event Management

### GET /events
Get list of upcoming events.

**Query Parameters:**
- `category` (optional): Filter by event category
- `event_type` (optional): online, in_person, hybrid
- `start_date` (optional): Filter events after this date
- `skip` (optional): Pagination offset
- `limit` (optional): Maximum results

**Response:**
```json
{
  "events": [
    {
      "id": "uuid-string",
      "title": "Full Moon Meditation Circle",
      "description": "Join us for a powerful full moon meditation...",
      "event_date": "2024-02-24T19:00:00Z",
      "duration": 90,
      "capacity": 20,
      "current_attendees": 12,
      "price": 25.0,
      "event_type": "online",
      "status": "published",
      "host": {
        "user": {
          "full_name": "Sarah Johnson"
        }
      }
    }
  ],
  "total": 1
}
```

### POST /events
Create a new event (coach only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "New Moon Intention Setting",
  "description": "A powerful session to set intentions...",
  "event_date": "2024-03-10T18:00:00Z",
  "duration": 120,
  "capacity": 15,
  "price": 30.0,
  "event_type": "online",
  "category": "Meditation Circle",
  "meeting_link": "https://zoom.us/j/987654321"
}
```

### POST /events/{event_id}/rsvp
RSVP to an event.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "registration_notes": "Excited to join!",
  "dietary_restrictions": "Vegetarian"
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "event_id": "uuid-string",
  "status": "pending",
  "payment_required": true,
  "amount_paid": 0.0,
  "stripe_checkout_url": "https://checkout.stripe.com/..."
}
```

## Payment Processing

### POST /payments/create-checkout-session
Create a Stripe checkout session for booking payment.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "booking_id": "uuid-string"
}
```

**Response:**
```json
{
  "checkout_url": "https://checkout.stripe.com/pay/cs_...",
  "session_id": "cs_..."
}
```

### POST /payments/webhook
Stripe webhook endpoint for payment events (internal use).

## Dashboard

### GET /me/dashboard
Get personalized dashboard data.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "upcoming_sessions": [
    {
      "id": "uuid-string",
      "session_time": "2024-02-15T14:00:00Z",
      "coach_name": "Sarah Johnson",
      "session_type": "Life Coaching"
    }
  ],
  "recent_events": [
    {
      "id": "uuid-string",
      "title": "Full Moon Circle",
      "event_date": "2024-02-24T19:00:00Z",
      "rsvp_status": "confirmed"
    }
  ],
  "testimonials_count": 3,
  "total_sessions": 12
}
```

## Error Responses

All endpoints return structured error responses:

```json
{
  "detail": "Error message description"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```
