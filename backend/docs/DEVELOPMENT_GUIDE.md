# Development Guide

## Overview

This guide provides comprehensive information for developers working on the Hi-Vi Spiritual Coaching Platform backend. It covers setup, development workflows, coding standards, and best practices.

## Quick Start

### Prerequisites

- Python 3.11+
- [uv](https://docs.astral.sh/uv/) for dependency management
- Docker and Docker Compose
- PostgreSQL (for local development)
- Git

### Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-org/hivi.git
   cd hivi/backend
   ```

2. **Install dependencies:**
   ```bash
   uv sync
   ```

3. **Activate virtual environment:**
   ```bash
   source .venv/bin/activate
   ```

4. **Set up environment variables:**
   ```bash
   cp ../.env.example ../.env
   # Edit .env with your local configuration
   ```

5. **Start the database:**
   ```bash
   docker compose up -d postgres
   ```

6. **Run database migrations:**
   ```bash
   alembic upgrade head
   ```

7. **Start the development server:**
   ```bash
   fastapi run app/main.py --reload
   ```

The API will be available at `http://localhost:8000` with interactive docs at `http://localhost:8000/docs`.

## Project Structure

```
backend/
├── app/
│   ├── api/                 # API routes and endpoints
│   │   ├── routes/         # Individual route modules
│   │   ├── deps.py         # Shared dependencies
│   │   └── main.py         # Router configuration
│   ├── core/               # Core configuration
│   │   ├── config.py       # Application settings
│   │   ├── db.py           # Database configuration
│   │   └── security.py     # Authentication utilities
│   ├── models/             # SQLModel database models
│   ├── schemas/            # Pydantic request/response models
│   ├── services/           # Business logic layer
│   ├── repositories/       # Data access layer
│   ├── utils/              # Utility functions
│   ├── tests/              # Test files
│   └── main.py             # FastAPI application entry point
├── docs/                   # Documentation
├── scripts/                # Development scripts
├── pyproject.toml          # Project configuration
└── README.md
```

## Development Workflow

### Feature Development

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes following the coding standards**

3. **Write tests for your changes:**
   ```bash
   # Run tests
   pytest

   # Run with coverage
   pytest --cov=app
   ```

4. **Run linting and formatting:**
   ```bash
   # Check code style
   ruff check .

   # Format code
   ruff format .

   # Type checking
   mypy .
   ```

5. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

6. **Push and create a pull request:**
   ```bash
   git push origin feature/your-feature-name
   ```

### Database Changes

When modifying database models:

1. **Update the model in `app/models/`**

2. **Create a migration:**
   ```bash
   alembic revision --autogenerate -m "Description of changes"
   ```

3. **Review the generated migration file**

4. **Apply the migration:**
   ```bash
   alembic upgrade head
   ```

5. **Test the migration with sample data**

## Coding Standards

### Python Style Guide

We follow PEP 8 with some modifications enforced by Ruff:

- **Line length**: 88 characters (Black default)
- **Import sorting**: isort style
- **String quotes**: Double quotes preferred
- **Type hints**: Required for all public functions

### Code Organization

#### Models (`app/models/`)
- One model per file
- Use SQLModel for database models
- Include proper relationships and constraints
- Add docstrings for complex models

```python
from sqlmodel import SQLModel, Field, Relationship
from datetime import datetime
import uuid

class User(SQLModel, table=True):
    """User model for authentication and profile management."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    email: str = Field(unique=True, index=True, max_length=255)
    full_name: str | None = Field(default=None, max_length=255)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    coach_profile: "CoachProfile" = Relationship(back_populates="user")
```

#### Schemas (`app/schemas/`)
- Separate schemas for create, update, and response
- Use Pydantic for validation
- Include examples for API documentation

```python
from pydantic import BaseModel, EmailStr
from datetime import datetime
import uuid

class UserCreate(BaseModel):
    """Schema for creating a new user."""
    email: EmailStr
    password: str
    full_name: str | None = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "password": "secure_password",
                "full_name": "John Doe"
            }
        }
    }

class UserResponse(BaseModel):
    """Schema for user API responses."""
    id: uuid.UUID
    email: str
    full_name: str | None
    created_at: datetime

    model_config = {"from_attributes": True}
```

#### Services (`app/services/`)
- Business logic and orchestration
- Dependency injection for repositories
- Error handling and validation

```python
from sqlmodel import Session
from app.repositories.user_repository import UserRepository
from app.schemas.user import UserCreate, UserUpdate

class UserService:
    """Service for user-related business logic."""

    def __init__(self):
        self.user_repo = UserRepository()

    def create_user(self, session: Session, *, user_data: UserCreate) -> User:
        """Create a new user with validation."""
        # Business logic here
        return self.user_repo.create(session, obj_in=user_data)
```

#### Repositories (`app/repositories/`)
- Data access layer
- Extend BaseRepository for common operations
- Database-specific logic only

```python
from app.repositories.base_repository import BaseRepository
from app.models.user import User

class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """Repository for user data access."""

    def get_by_email(self, session: Session, *, email: str) -> User | None:
        """Get user by email address."""
        return session.query(User).filter(User.email == email).first()
```

#### API Routes (`app/api/routes/`)
- Thin controllers that delegate to services
- Proper HTTP status codes
- Comprehensive error handling

```python
from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import Session
from app.api.deps import get_current_user, get_session
from app.services.user_service import UserService
from app.schemas.user import UserResponse, UserUpdate

router = APIRouter()
user_service = UserService()

@router.get("/me", response_model=UserResponse)
def get_current_user_profile(
    current_user: User = Depends(get_current_user)
) -> UserResponse:
    """Get current user profile."""
    return UserResponse.model_validate(current_user)

@router.put("/me", response_model=UserResponse)
def update_current_user(
    *,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user),
    user_data: UserUpdate
) -> UserResponse:
    """Update current user profile."""
    updated_user = user_service.update_user(
        session, user=current_user, user_data=user_data
    )
    return UserResponse.model_validate(updated_user)
```

### Error Handling

Use structured error responses:

```python
from fastapi import HTTPException

# Business logic errors
raise HTTPException(
    status_code=400,
    detail="Invalid booking time: session must be in the future"
)

# Not found errors
raise HTTPException(
    status_code=404,
    detail="Coach profile not found"
)

# Permission errors
raise HTTPException(
    status_code=403,
    detail="Insufficient permissions to access this resource"
)
```

### Testing Standards

#### Unit Tests
- Test business logic in services
- Mock external dependencies
- Use pytest fixtures for common setup

```python
import pytest
from unittest.mock import Mock
from app.services.coach_service import CoachService
from app.schemas.coach import CoachProfileCreate

@pytest.fixture
def mock_coach_repo():
    return Mock()

@pytest.fixture
def coach_service(mock_coach_repo):
    service = CoachService()
    service.coach_repo = mock_coach_repo
    return service

def test_create_coach_profile(coach_service, mock_coach_repo):
    # Arrange
    user_id = uuid.uuid4()
    coach_data = CoachProfileCreate(bio="Test bio", hourly_rate=75.0)

    # Act
    result = coach_service.create_coach_profile(
        session=Mock(), user_id=user_id, coach_data=coach_data
    )

    # Assert
    mock_coach_repo.create.assert_called_once()
    assert result is not None
```

#### Integration Tests
- Test API endpoints end-to-end
- Use test database
- Test authentication and authorization

```python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_coach_profile_authenticated():
    # Login and get token
    login_response = client.post("/api/v1/login/access-token", data={
        "username": "<EMAIL>",
        "password": "testpass"
    })
    token = login_response.json()["access_token"]

    # Create coach profile
    response = client.post(
        "/api/v1/coaches/profile",
        json={"bio": "Test bio", "hourly_rate": 75.0},
        headers={"Authorization": f"Bearer {token}"}
    )

    assert response.status_code == 201
    assert response.json()["bio"] == "Test bio"
```

## Environment Configuration

### Local Development (.env)
```bash
# Application
ENVIRONMENT=local
PROJECT_NAME="Hi-Vi Development"
SECRET_KEY=your-local-secret-key

# Database
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=hivi_dev
POSTGRES_PASSWORD=dev_password
POSTGRES_DB=hivi_dev

# Stripe (use test keys)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_test_...

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
FRONTEND_HOST=http://localhost:5173
```

### Testing (.env.test)
```bash
# Use separate test database
POSTGRES_DB=hivi_test
# Disable external services
STRIPE_SECRET_KEY=sk_test_fake
EMAILS_ENABLED=false
```

## Debugging

### VS Code Configuration

The project includes VS Code configurations for debugging:

1. **Python Debugger**: Set breakpoints and debug the FastAPI application
2. **Test Debugger**: Debug individual tests
3. **Docker Debugger**: Debug the application running in Docker

### Logging

Use structured logging throughout the application:

```python
import logging

logger = logging.getLogger(__name__)

def create_booking(session: Session, booking_data: BookingCreate):
    logger.info(f"Creating booking for user {booking_data.user_id}")
    try:
        # Business logic
        logger.info(f"Booking created successfully: {booking.id}")
        return booking
    except Exception as e:
        logger.error(f"Failed to create booking: {str(e)}")
        raise
```

## Performance Considerations

### Database Optimization
- Use proper indexes on frequently queried fields
- Implement connection pooling
- Use database transactions appropriately
- Monitor slow queries

### Caching Strategy
- Cache frequently accessed data (coach profiles, categories)
- Use Redis for session storage
- Implement cache invalidation strategies

### API Performance
- Use pagination for list endpoints
- Implement proper filtering and sorting
- Monitor response times
- Use async/await for I/O operations

## Security Best Practices

### Authentication & Authorization
- Use JWT tokens with appropriate expiration
- Implement role-based access control
- Validate all user inputs
- Use HTTPS in production

### Data Protection
- Hash passwords with bcrypt
- Sanitize user inputs
- Use parameterized queries (SQLModel handles this)
- Implement rate limiting

### API Security
- Validate request payloads
- Use CORS appropriately
- Implement proper error handling
- Log security events

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check PostgreSQL is running
   - Verify connection string in .env
   - Ensure database exists

2. **Migration Errors**
   - Check for conflicting migrations
   - Verify model changes are correct
   - Use `alembic downgrade` if needed

3. **Import Errors**
   - Ensure virtual environment is activated
   - Check PYTHONPATH includes app directory
   - Verify all dependencies are installed

4. **Test Failures**
   - Use separate test database
   - Clear test data between tests
   - Check for async/await issues

### Getting Help

- Check the documentation in `/docs`
- Review existing tests for examples
- Ask questions in team chat
- Create GitHub issues for bugs
