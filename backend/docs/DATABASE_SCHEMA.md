# Database Schema Documentation

## Overview

The Hi-Vi Spiritual Coaching Platform uses PostgreSQL as the primary database with SQLModel for type-safe database operations. This document describes the database schema, relationships, and design decisions.

## Entity Relationship Diagram

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│    User     │    │ CoachProfile │    │  Category   │
│             │    │              │    │             │
│ id (PK)     │◄──►│ id (PK)      │───►│ id (PK)     │
│ email       │    │ user_id (FK) │    │ name        │
│ password    │    │ bio          │    │ description │
│ role        │    │ hourly_rate  │    └─────────────┘
│ full_name   │    │ category_id  │
│ is_active   │    │ location_id  │    ┌─────────────┐
│ created_at  │    │ avg_rating   │    │  Location   │
└─────────────┘    │ total_reviews│───►│             │
       │           └──────────────┘    │ id (PK)     │
       │                               │ name        │
       │           ┌──────────────┐    │ type        │
       │           │   Booking    │    │ address     │
       └──────────►│              │    └─────────────┘
                   │ id (PK)      │
                   │ user_id (FK) │
                   │ coach_id(FK) │
                   │ session_time │
                   │ duration     │
                   │ status       │
                   │ notes        │
                   └──────────────┘
```

## Core Tables

### User Table

The central user table supporting role-based access control.

```sql
CREATE TABLE user (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    full_name VARCHAR(255),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'coach', 'admin')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_email ON user(email);
CREATE INDEX idx_user_role ON user(role);
```

**Key Fields:**
- `id`: UUID primary key
- `email`: Unique email address for authentication
- `role`: Enum-like field with check constraint (user, coach, admin)
- `hashed_password`: Bcrypt-hashed password
- `is_active`: Soft delete flag
- `is_superuser`: Admin privileges flag

### CoachProfile Table

Extended profile information for users with coach role.

```sql
CREATE TABLE coachprofile (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    bio TEXT,
    experience_years INTEGER CHECK (experience_years >= 0),
    hourly_rate DECIMAL(10,2) CHECK (hourly_rate >= 0),
    is_available BOOLEAN DEFAULT TRUE,
    profile_image_url VARCHAR(500),
    category_id UUID REFERENCES category(id),
    location_id UUID REFERENCES location(id),
    average_rating DECIMAL(3,2) CHECK (average_rating >= 0 AND average_rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_coach_user_id ON coachprofile(user_id);
CREATE INDEX idx_coach_category ON coachprofile(category_id);
CREATE INDEX idx_coach_location ON coachprofile(location_id);
CREATE INDEX idx_coach_rating ON coachprofile(average_rating);
CREATE INDEX idx_coach_available ON coachprofile(is_available);
```

**Key Fields:**
- `user_id`: One-to-one relationship with User table
- `bio`: Rich text coach biography
- `hourly_rate`: Coaching session rate
- `average_rating`: Calculated field updated via triggers
- `total_reviews`: Cached count for performance

### Category Table

Spiritual coaching categories for coach specialization.

```sql
CREATE TABLE category (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_category_name ON category(name);
```

**Predefined Categories:**
- Mindset & Emotional Growth Coaches
- Intuitive Guides & Spiritual Mentors
- Energy & Frequency Healing
- Holistic Wellness Practitioners
- Ascension & Transformation Coaches

### Location Table

Location information for coaches and events.

```sql
CREATE TABLE location (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) CHECK (type IN ('city', 'state', 'country', 'virtual')),
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_location_type ON location(type);
CREATE INDEX idx_location_name ON location(name);
```

### Booking Table

Session bookings between users and coaches.

```sql
CREATE TABLE booking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    coach_id UUID NOT NULL REFERENCES coachprofile(id) ON DELETE CASCADE,
    session_time TIMESTAMP NOT NULL,
    duration INTEGER DEFAULT 60 CHECK (duration >= 15 AND duration <= 480),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    notes TEXT,
    session_type VARCHAR(100),
    meeting_link VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_booking_user ON booking(user_id);
CREATE INDEX idx_booking_coach ON booking(coach_id);
CREATE INDEX idx_booking_session_time ON booking(session_time);
CREATE INDEX idx_booking_status ON booking(status);
```

**Key Fields:**
- `session_time`: Scheduled session date/time
- `duration`: Session length in minutes (15min to 8hrs)
- `status`: Booking lifecycle status
- `meeting_link`: Video call URL for online sessions

### Event Table

Group events and spiritual circles hosted by coaches.

```sql
CREATE TABLE event (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    host_id UUID NOT NULL REFERENCES coachprofile(id) ON DELETE CASCADE,
    event_date TIMESTAMP NOT NULL,
    duration INTEGER DEFAULT 90 CHECK (duration >= 30 AND duration <= 480),
    capacity INTEGER DEFAULT 20 CHECK (capacity >= 1 AND capacity <= 1000),
    current_attendees INTEGER DEFAULT 0 CHECK (current_attendees >= 0),
    price DECIMAL(10,2) DEFAULT 0.0 CHECK (price >= 0),
    currency VARCHAR(3) DEFAULT 'usd',
    event_type VARCHAR(20) DEFAULT 'online' CHECK (event_type IN ('online', 'in_person', 'hybrid')),
    location_id UUID REFERENCES location(id),
    address VARCHAR(500),
    meeting_link VARCHAR(500),
    meeting_password VARCHAR(100),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'cancelled', 'completed')),
    registration_deadline TIMESTAMP,
    allow_waitlist BOOLEAN DEFAULT TRUE,
    require_approval BOOLEAN DEFAULT FALSE,
    category VARCHAR(100),
    tags VARCHAR(500),
    what_to_bring TEXT,
    prerequisites TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_event_host ON event(host_id);
CREATE INDEX idx_event_date ON event(event_date);
CREATE INDEX idx_event_status ON event(status);
CREATE INDEX idx_event_type ON event(event_type);
CREATE INDEX idx_event_category ON event(category);
```

### EventRSVP Table

Event registrations and RSVPs.

```sql
CREATE TABLE eventrsvp (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES event(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'attended', 'no_show')),
    payment_required BOOLEAN DEFAULT FALSE,
    payment_completed BOOLEAN DEFAULT FALSE,
    amount_paid DECIMAL(10,2) DEFAULT 0.0 CHECK (amount_paid >= 0),
    stripe_checkout_session_id VARCHAR(255),
    stripe_payment_intent_id VARCHAR(255),
    registration_notes TEXT,
    dietary_restrictions VARCHAR(500),
    is_waitlisted BOOLEAN DEFAULT FALSE,
    waitlist_position INTEGER,
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id, user_id)
);

CREATE INDEX idx_rsvp_event ON eventrsvp(event_id);
CREATE INDEX idx_rsvp_user ON eventrsvp(user_id);
CREATE INDEX idx_rsvp_status ON eventrsvp(status);
CREATE INDEX idx_rsvp_waitlist ON eventrsvp(is_waitlisted);
```

### Payment Table

Payment records for bookings and events.

```sql
CREATE TABLE payment (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES user(id),
    booking_id UUID REFERENCES booking(id),
    event_rsvp_id UUID REFERENCES eventrsvp(id),
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'usd',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded')),
    stripe_payment_intent_id VARCHAR(255),
    stripe_checkout_session_id VARCHAR(255),
    description TEXT,
    refund_amount DECIMAL(10,2) CHECK (refund_amount >= 0),
    refund_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at TIMESTAMP
);

CREATE INDEX idx_payment_user ON payment(user_id);
CREATE INDEX idx_payment_booking ON payment(booking_id);
CREATE INDEX idx_payment_event_rsvp ON payment(event_rsvp_id);
CREATE INDEX idx_payment_status ON payment(status);
CREATE INDEX idx_payment_stripe_intent ON payment(stripe_payment_intent_id);
```

### Review Table

Coach reviews and testimonials.

```sql
CREATE TABLE review (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reviewer_id UUID NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    coach_profile_id UUID NOT NULL REFERENCES coachprofile(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES booking(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_testimonial BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(reviewer_id, booking_id)
);

CREATE INDEX idx_review_coach ON review(coach_profile_id);
CREATE INDEX idx_review_reviewer ON review(reviewer_id);
CREATE INDEX idx_review_rating ON review(rating);
CREATE INDEX idx_review_testimonial ON review(is_testimonial);
CREATE INDEX idx_review_public ON review(is_public);
```

### CoachAvailability Table

Coach weekly availability schedule.

```sql
CREATE TABLE coachavailability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coach_id UUID NOT NULL REFERENCES coachprofile(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(coach_id, day_of_week, start_time)
);

CREATE INDEX idx_availability_coach ON coachavailability(coach_id);
CREATE INDEX idx_availability_day ON coachavailability(day_of_week);
```

## Database Triggers and Functions

### Update Rating Trigger

Automatically updates coach average rating when reviews are added/updated:

```sql
CREATE OR REPLACE FUNCTION update_coach_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE coachprofile
    SET
        average_rating = (
            SELECT AVG(rating::DECIMAL)
            FROM review
            WHERE coach_profile_id = COALESCE(NEW.coach_profile_id, OLD.coach_profile_id)
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM review
            WHERE coach_profile_id = COALESCE(NEW.coach_profile_id, OLD.coach_profile_id)
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = COALESCE(NEW.coach_profile_id, OLD.coach_profile_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_coach_rating
    AFTER INSERT OR UPDATE OR DELETE ON review
    FOR EACH ROW
    EXECUTE FUNCTION update_coach_rating();
```

### Update Timestamps Trigger

Automatically updates `updated_at` timestamps:

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables with updated_at column
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON user
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coach_updated_at BEFORE UPDATE ON coachprofile
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- ... (similar triggers for other tables)
```

## Data Integrity Constraints

### Foreign Key Constraints
- All foreign keys use CASCADE DELETE where appropriate
- Coach profiles are deleted when user is deleted
- Bookings and reviews are preserved with coach profile deletion

### Check Constraints
- Rating values between 1-5
- Duration limits for sessions and events
- Positive values for prices and rates
- Valid enum values for status fields

### Unique Constraints
- One coach profile per user
- One review per booking per user
- One RSVP per event per user

## Performance Optimizations

### Indexing Strategy
- Primary keys (automatic B-tree indexes)
- Foreign keys for join performance
- Frequently queried fields (email, status, dates)
- Composite indexes for complex queries

### Query Optimization
- Materialized views for complex aggregations
- Partial indexes for filtered queries
- Connection pooling for concurrent access

### Caching Strategy
- Application-level caching for frequently accessed data
- Database query result caching
- Redis for session and temporary data

## Migration Management

Database schema changes are managed through Alembic migrations:

```bash
# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback migrations
alembic downgrade -1
```

## Backup and Recovery

### Backup Strategy
- Daily automated backups
- Point-in-time recovery capability
- Cross-region backup replication

### Recovery Procedures
- Database restoration from backups
- Transaction log replay for point-in-time recovery
- Disaster recovery procedures documented
