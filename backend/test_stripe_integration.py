#!/usr/bin/env python3
"""
Test script to verify Stripe integration is working correctly.
"""

import os
import sys
import uuid
from datetime import datetime, timedel<PERSON>

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import settings
from app.models.payment import Payment, PaymentStatus
from app.schemas.payment import StripeCheckoutRequest


def test_stripe_import():
    """Test that Stripe can be imported successfully."""
    try:
        import stripe
        print("✅ Stripe imported successfully")
        try:
            print(f"   Stripe version: {stripe.version.VERSION}")
        except:
            print("   Stripe version: Available")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Stripe: {e}")
        return False


def test_stripe_configuration():
    """Test that Stripe configuration is loaded."""
    try:
        print("✅ Stripe configuration loaded:")
        print(f"   Publishable key configured: {'Yes' if settings.STRIPE_PUBLISHABLE_KEY else 'No'}")
        print(f"   Secret key configured: {'Yes' if settings.STRIPE_SECRET_KEY else 'No'}")
        print(f"   Webhook secret configured: {'Yes' if settings.STRIPE_WEBHOOK_SECRET else 'No'}")
        print(f"   Success URL: {settings.STRIPE_SUCCESS_URL}")
        print(f"   Cancel URL: {settings.STRIPE_CANCEL_URL}")
        return True
    except Exception as e:
        print(f"❌ Failed to load Stripe configuration: {e}")
        return False


def test_payment_models():
    """Test that payment models can be created."""
    try:
        # Test Payment model
        payment = Payment(
            booking_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            amount=100.0,
            currency="usd",
            status=PaymentStatus.PENDING.value,
            description="Test spiritual coaching session"
        )
        print("✅ Payment model created successfully")
        print(f"   Payment ID: {payment.id}")
        print(f"   Amount: ${payment.amount}")
        print(f"   Status: {payment.status}")

        # Test StripeCheckoutRequest schema
        checkout_request = StripeCheckoutRequest(
            booking_id=uuid.uuid4(),
            success_url="http://localhost:3000/booking/success",
            cancel_url="http://localhost:3000/booking/cancel"
        )
        print("✅ StripeCheckoutRequest schema created successfully")
        print(f"   Booking ID: {checkout_request.booking_id}")

        return True
    except Exception as e:
        print(f"❌ Failed to create payment models: {e}")
        return False


def test_stripe_service_import():
    """Test that StripeService can be imported."""
    try:
        from app.services.stripe_service import StripeService
        stripe_service = StripeService()
        print("✅ StripeService imported and instantiated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import StripeService: {e}")
        return False


def main():
    """Run all Stripe integration tests."""
    print("🧪 Testing Stripe Integration for Spiritual Coaching Platform")
    print("=" * 60)

    tests = [
        test_stripe_import,
        test_stripe_configuration,
        test_payment_models,
        test_stripe_service_import,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 40)

    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All Stripe integration tests passed!")
        print("\n✨ Your spiritual coaching platform is ready for payments!")
        print("\n📋 Next steps:")
        print("   1. Add your actual Stripe keys to .env file")
        print("   2. Set up Stripe webhook endpoint")
        print("   3. Test with real Stripe checkout")
        print("   4. Configure frontend payment flow")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
