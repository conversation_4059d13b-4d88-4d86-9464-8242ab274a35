#!/bin/bash

# Hi-Vi Infrastructure Deployment Script
# Usage: ./scripts/deploy.sh [environment] [domain] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
DOMAIN_NAME=""
CERTIFICATE_ARN=""
DRY_RUN=false
SKIP_TESTS=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Hi-Vi Infrastructure Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV       Environment to deploy (development|staging|production)
    -d, --domain DOMAIN        Domain name (e.g., hi-vi.com)
    -c, --certificate ARN      Existing SSL certificate ARN (optional)
    --dry-run                  Show what would be deployed without actually deploying
    --skip-tests              Skip running tests before deployment
    -h, --help                Show this help message

EXAMPLES:
    # Deploy development environment
    $0 -e development -d dev.hi-vi.com

    # Deploy production with existing certificate
    $0 -e production -d hi-vi.com -c arn:aws:acm:us-east-1:123456789012:certificate/...

    # Dry run for staging
    $0 -e staging -d staging.hi-vi.com --dry-run

ENVIRONMENTS:
    development    - Single instance, minimal resources, 7-day retention
    staging        - Single instance, minimal resources, 7-day retention
    production     - Multi-instance, enhanced resources, 30-day retention

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN_NAME="$2"
            shift 2
            ;;
        -c|--certificate)
            CERTIFICATE_ARN="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required. Use -e or --environment"
    show_usage
    exit 1
fi

if [[ -z "$DOMAIN_NAME" ]]; then
    print_error "Domain name is required. Use -d or --domain"
    show_usage
    exit 1
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment. Must be: development, staging, or production"
    exit 1
fi

# Set stack name based on environment
case $ENVIRONMENT in
    development)
        STACK_NAME="HiViDevelopmentStack"
        ;;
    staging)
        STACK_NAME="HiViStagingStack"
        ;;
    production)
        STACK_NAME="HiViProductionStack"
        ;;
esac

print_status "Starting Hi-Vi infrastructure deployment"
print_status "Environment: $ENVIRONMENT"
print_status "Domain: $DOMAIN_NAME"
print_status "Stack: $STACK_NAME"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid"
    print_error "Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

# Get AWS account and region
AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION=$(aws configure get region || echo "us-east-1")

print_status "AWS Account: $AWS_ACCOUNT"
print_status "AWS Region: $AWS_REGION"

# Check if CDK is bootstrapped
print_status "Checking CDK bootstrap status..."
if ! aws cloudformation describe-stacks --stack-name CDKToolkit --region $AWS_REGION &> /dev/null; then
    print_warning "CDK is not bootstrapped in this account/region"
    print_status "Bootstrapping CDK..."
    cdk bootstrap aws://$AWS_ACCOUNT/$AWS_REGION
    print_success "CDK bootstrap completed"
fi

# Install dependencies if needed
if [[ ! -d "node_modules" ]]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Run tests unless skipped
if [[ "$SKIP_TESTS" == false ]]; then
    print_status "Running tests..."
    npm test
    print_success "All tests passed"
fi

# Build the project
print_status "Building TypeScript..."
npm run build
print_success "Build completed"

# Prepare CDK context
CDK_CONTEXT="--context environment=$ENVIRONMENT --context domainName=$DOMAIN_NAME"

if [[ -n "$CERTIFICATE_ARN" ]]; then
    CDK_CONTEXT="$CDK_CONTEXT --context certificateArn=$CERTIFICATE_ARN"
fi

# Show what will be deployed
print_status "Generating deployment plan..."
if [[ "$DRY_RUN" == true ]]; then
    print_status "DRY RUN - Showing what would be deployed:"
    cdk diff $STACK_NAME $CDK_CONTEXT
    print_warning "This was a dry run. No resources were actually deployed."
    exit 0
fi

# Show diff before deployment
print_status "Showing changes to be deployed:"
cdk diff $STACK_NAME $CDK_CONTEXT

# Confirm deployment for production
if [[ "$ENVIRONMENT" == "production" ]]; then
    print_warning "You are about to deploy to PRODUCTION environment!"
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
fi

# Deploy the stack
print_status "Deploying $STACK_NAME..."
cdk deploy $STACK_NAME $CDK_CONTEXT --require-approval never

# Get stack outputs
print_status "Retrieving stack outputs..."
OUTPUTS=$(aws cloudformation describe-stacks \
    --stack-name $(echo $STACK_NAME | sed 's/Stack$//' | tr '[:upper:]' '[:lower:]')-$ENVIRONMENT \
    --query 'Stacks[0].Outputs' \
    --output table 2>/dev/null || echo "No outputs available")

print_success "Deployment completed successfully!"
echo
print_status "Stack Outputs:"
echo "$OUTPUTS"

# Show next steps
echo
print_status "Next Steps:"
echo "1. Update your DNS records to point to the load balancer"
echo "2. Update Stripe webhook URL in your Stripe dashboard"
echo "3. Configure your CI/CD pipeline to push images to ECR"
echo "4. Update application secrets in AWS Secrets Manager"
echo
print_status "Useful commands:"
echo "  View logs: aws logs tail /ecs/hivi-backend --follow"
echo "  Check service: aws ecs describe-services --cluster hivi-cluster --services hivi-backend"
echo "  Update secrets: aws secretsmanager update-secret --secret-id hivi/stripe-secret --secret-string 'your-key'"

print_success "Hi-Vi infrastructure deployment completed!"
