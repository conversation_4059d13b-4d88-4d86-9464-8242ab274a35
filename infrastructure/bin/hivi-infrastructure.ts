#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { HiViStack } from '../lib/hivi-stack';

const app = new cdk.App();

// Get environment from context or default to development
const environment = app.node.tryGetContext('environment') || 'development';
const domainName = app.node.tryGetContext('domainName') || 'hi-vi.com';
const certificateArn = app.node.tryGetContext('certificateArn');

// Development Stack
if (environment === 'development') {
  new HiViStack(app, 'HiViDevelopmentStack', {
    env: {
      account: process.env.CDK_DEFAULT_ACCOUNT,
      region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
    },
    domainName: `dev.${domainName}`,
    environment: 'development',
    stackName: 'hivi-development',
    description: 'Hi-Vi Spiritual Coaching Platform - Development Environment',
    tags: {
      Environment: 'development',
      Project: 'Hi-Vi',
      Owner: 'Development Team',
    },
  });
}

// Staging Stack
if (environment === 'staging') {
  new HiViStack(app, 'HiViStagingStack', {
    env: {
      account: process.env.CDK_DEFAULT_ACCOUNT,
      region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
    },
    domainName: `staging.${domainName}`,
    environment: 'staging',
    stackName: 'hivi-staging',
    description: 'Hi-Vi Spiritual Coaching Platform - Staging Environment',
    tags: {
      Environment: 'staging',
      Project: 'Hi-Vi',
      Owner: 'Development Team',
    },
  });
}

// Production Stack
if (environment === 'production') {
  new HiViStack(app, 'HiViProductionStack', {
    env: {
      account: process.env.CDK_DEFAULT_ACCOUNT,
      region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
    },
    domainName: domainName,
    environment: 'production',
    certificateArn: certificateArn,
    stackName: 'hivi-production',
    description: 'Hi-Vi Spiritual Coaching Platform - Production Environment',
    tags: {
      Environment: 'production',
      Project: 'Hi-Vi',
      Owner: 'Hi-Vi Team',
      CostCenter: 'Production',
    },
  });
}

app.synth();
