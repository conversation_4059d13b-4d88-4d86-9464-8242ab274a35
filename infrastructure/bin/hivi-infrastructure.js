#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = __importStar(require("aws-cdk-lib"));
const hivi_stack_1 = require("../lib/hivi-stack");
const app = new cdk.App();
// Get environment from context or default to development
const environment = app.node.tryGetContext('environment') || 'development';
const domainName = app.node.tryGetContext('domainName') || 'hi-vi.com';
const certificateArn = app.node.tryGetContext('certificateArn');
// Development Stack
if (environment === 'development') {
    new hivi_stack_1.HiViStack(app, 'HiViDevelopmentStack', {
        env: {
            account: process.env.CDK_DEFAULT_ACCOUNT,
            region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
        },
        domainName: `dev.${domainName}`,
        environment: 'development',
        stackName: 'hivi-development',
        description: 'Hi-Vi Spiritual Coaching Platform - Development Environment',
        tags: {
            Environment: 'development',
            Project: 'Hi-Vi',
            Owner: 'Development Team',
        },
    });
}
// Staging Stack
if (environment === 'staging') {
    new hivi_stack_1.HiViStack(app, 'HiViStagingStack', {
        env: {
            account: process.env.CDK_DEFAULT_ACCOUNT,
            region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
        },
        domainName: `staging.${domainName}`,
        environment: 'staging',
        stackName: 'hivi-staging',
        description: 'Hi-Vi Spiritual Coaching Platform - Staging Environment',
        tags: {
            Environment: 'staging',
            Project: 'Hi-Vi',
            Owner: 'Development Team',
        },
    });
}
// Production Stack
if (environment === 'production') {
    new hivi_stack_1.HiViStack(app, 'HiViProductionStack', {
        env: {
            account: process.env.CDK_DEFAULT_ACCOUNT,
            region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
        },
        domainName: domainName,
        environment: 'production',
        certificateArn: certificateArn,
        stackName: 'hivi-production',
        description: 'Hi-Vi Spiritual Coaching Platform - Production Environment',
        tags: {
            Environment: 'production',
            Project: 'Hi-Vi',
            Owner: 'Hi-Vi Team',
            CostCenter: 'Production',
        },
    });
}
app.synth();
//# sourceMappingURL=data:application/json;base64,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