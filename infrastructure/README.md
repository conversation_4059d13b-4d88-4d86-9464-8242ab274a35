# Hi-Vi Infrastructure

AWS CDK infrastructure as code for the Hi-Vi Spiritual Coaching Platform.

## Overview

This CDK project defines the complete AWS infrastructure for the Hi-Vi platform, including:

- **VPC** with public, private, and isolated subnets
- **ECS Fargate** cluster for containerized backend
- **RDS PostgreSQL** database with encryption and backups
- **Application Load Balancer** with SSL termination
- **ECR** repository for Docker images
- **Auto Scaling** based on CPU and memory metrics
- **CloudWatch** monitoring and alarms
- **Secrets Manager** for secure credential storage
- **Route 53** DNS management
- **ACM** SSL certificates

## Prerequisites

- Node.js 18+ and npm
- AWS CLI configured with appropriate credentials
- AWS CDK CLI installed globally: `npm install -g aws-cdk`
- Docker (for building and pushing images)

## Quick Start

1. **Install dependencies:**
   ```bash
   cd infrastructure
   npm install
   ```

2. **Bootstrap CDK (first time only):**
   ```bash
   cdk bootstrap
   ```

3. **Deploy development environment:**
   ```bash
   npm run deploy:dev
   ```

## Project Structure

```
infrastructure/
├── bin/
│   └── hivi-infrastructure.ts    # CDK app entry point
├── lib/
│   └── hivi-stack.ts            # Main infrastructure stack
├── test/
│   ├── hivi-stack.test.ts       # Stack unit tests
│   └── setup.ts                 # Test configuration
├── cdk.json                     # CDK configuration
├── package.json                 # Dependencies and scripts
├── tsconfig.json               # TypeScript configuration
└── README.md                   # This file
```

## Available Scripts

### Deployment Commands

```bash
# Deploy to development environment
npm run deploy:dev

# Deploy to staging environment
npm run deploy:staging

# Deploy to production environment
npm run deploy:prod
```

### Management Commands

```bash
# Show differences before deployment
npm run diff:dev
npm run diff:staging
npm run diff:prod

# Synthesize CloudFormation templates
npm run synth:dev
npm run synth:staging
npm run synth:prod

# Destroy environments (use with caution!)
npm run destroy:dev
npm run destroy:staging
npm run destroy:prod
```

### Development Commands

```bash
# Build TypeScript
npm run build

# Watch for changes
npm run watch

# Run tests
npm run test

# Run tests in watch mode
npm run test:watch
```

## Environment Configuration

The stack supports three environments with different configurations:

### Development
- **Domain:** `dev.hi-vi.com`
- **ECS Tasks:** 1 instance, 512 CPU, 1GB memory
- **RDS:** db.t3.micro, 20GB storage, 7-day backups
- **Auto Scaling:** 1-3 instances
- **Log Retention:** 7 days

### Staging
- **Domain:** `staging.hi-vi.com`
- **ECS Tasks:** 1 instance, 512 CPU, 1GB memory
- **RDS:** db.t3.micro, 20GB storage, 7-day backups
- **Auto Scaling:** 1-3 instances
- **Log Retention:** 7 days

### Production
- **Domain:** `hi-vi.com`
- **ECS Tasks:** 2 instances, 1024 CPU, 2GB memory
- **RDS:** db.t3.small, 100GB storage, 30-day backups
- **Auto Scaling:** 2-10 instances
- **Log Retention:** 30 days
- **Deletion Protection:** Enabled

## Custom Domain Setup

### Option 1: Let CDK manage DNS (Recommended)
The stack will automatically create a Route 53 hosted zone and SSL certificate:

```bash
cdk deploy --context domainName=your-domain.com
```

### Option 2: Use existing certificate
If you have an existing SSL certificate:

```bash
cdk deploy --context domainName=your-domain.com --context certificateArn=arn:aws:acm:...
```

## Secrets Management

The stack creates the following secrets in AWS Secrets Manager:

- `hivi/postgres-password` - Database password (auto-generated)
- `hivi/secret-key` - JWT secret key (auto-generated)
- `hivi/stripe-secret` - Stripe API key (placeholder, update manually)

### Updating Secrets

```bash
# Update Stripe secret key
aws secretsmanager update-secret \
  --secret-id hivi/stripe-secret \
  --secret-string "sk_live_your_actual_stripe_key"
```

## Database Access

### Connection Information
After deployment, get database connection details:

```bash
# Get database endpoint
aws rds describe-db-instances \
  --db-instance-identifier hivi-production \
  --query 'DBInstances[0].Endpoint.Address'

# Get database password
aws secretsmanager get-secret-value \
  --secret-id hivi/postgres-password \
  --query 'SecretString'
```

### Running Migrations
Database migrations are handled by the application deployment pipeline, but you can run them manually:

```bash
# Connect to ECS cluster and run migration task
aws ecs run-task \
  --cluster hivi-cluster \
  --task-definition hivi-migration \
  --launch-type FARGATE
```

## Monitoring and Logging

### CloudWatch Dashboards
The stack creates CloudWatch alarms for:
- High CPU utilization (>80%)
- High memory utilization (>85%)
- Application health checks

### Log Access
```bash
# View application logs
aws logs tail /ecs/hivi-backend --follow

# View specific log stream
aws logs get-log-events \
  --log-group-name /ecs/hivi-backend \
  --log-stream-name ecs/hivi-backend/task-id
```

## Auto Scaling

The ECS service automatically scales based on:
- **CPU Utilization:** Target 70%
- **Memory Utilization:** Target 80%
- **Scale Out:** 2-minute cooldown
- **Scale In:** 5-minute cooldown

## Security Features

- **VPC:** Private subnets for application and database
- **Security Groups:** Least privilege access rules
- **Encryption:** RDS encryption at rest, ALB SSL termination
- **Secrets:** AWS Secrets Manager for sensitive data
- **IAM:** Minimal required permissions for ECS tasks

## Cost Optimization

### Development/Staging
- Single NAT Gateway
- Smaller instance sizes
- Shorter log retention
- No deletion protection

### Production
- Multi-AZ deployment for high availability
- Larger instance sizes for performance
- Extended backup retention
- Deletion protection enabled

## Troubleshooting

### Common Issues

1. **Certificate validation timeout:**
   - Ensure DNS is properly configured
   - Check Route 53 hosted zone settings

2. **ECS tasks failing to start:**
   - Check CloudWatch logs for error messages
   - Verify ECR image exists and is accessible
   - Check security group rules

3. **Database connection issues:**
   - Verify security group allows traffic from ECS
   - Check database endpoint and credentials
   - Ensure database is in running state

### Useful Commands

```bash
# Check stack status
cdk list

# View stack outputs
aws cloudformation describe-stacks \
  --stack-name hivi-production \
  --query 'Stacks[0].Outputs'

# Check ECS service status
aws ecs describe-services \
  --cluster hivi-cluster \
  --services hivi-backend

# View recent ECS events
aws ecs describe-services \
  --cluster hivi-cluster \
  --services hivi-backend \
  --query 'services[0].events[:5]'
```

## Testing

Run the test suite to validate stack configuration:

```bash
npm test
```

Tests cover:
- Resource creation and configuration
- Security group rules
- Auto scaling policies
- Environment-specific settings

## Contributing

1. Make changes to the stack
2. Run tests: `npm test`
3. Check differences: `npm run diff:dev`
4. Deploy to development: `npm run deploy:dev`
5. Test the deployment
6. Create pull request

## Support

For infrastructure issues:
1. Check CloudWatch logs and alarms
2. Review AWS console for resource status
3. Run CDK diff to see pending changes
4. Contact the development team

## License

This infrastructure code is part of the Hi-Vi project and follows the same license terms.
