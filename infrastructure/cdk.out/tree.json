{"version": "tree-0.1", "tree": {"id": "App", "path": "", "children": {"HiViDevelopmentStack": {"id": "HiViDevelopmentStack", "path": "HiViDevelopmentStack", "children": {"HiViVpc": {"id": "HiViVpc", "path": "HiViDevelopmentStack/HiViVpc", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViVpc/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPC", "aws:cdk:cloudformation:props": {"cidrBlock": "10.0.0.0/16", "enableDnsHostnames": true, "enableDnsSupport": true, "instanceTenancy": "default", "tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPC", "version": "2.100.0"}}, "publicSubnet1": {"id": "publicSubnet1", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1a", "cidrBlock": "10.0.0.0/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcpublicSubnet1RouteTable4BBFB7E9"}, "subnetId": {"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "routeTableId": {"Ref": "HiViVpcpublicSubnet1RouteTable4BBFB7E9"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.100.0"}}, "EIP": {"id": "EIP", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/EIP", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::EIP", "aws:cdk:cloudformation:props": {"domain": "vpc", "tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnEIP", "version": "2.100.0"}}, "NATGateway": {"id": "NATGateway", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/NATGateway", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::NatGateway", "aws:cdk:cloudformation:props": {"allocationId": {"Fn::GetAtt": ["HiViVpcpublicSubnet1EIPAC015373", "AllocationId"]}, "subnetId": {"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}, "tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnNatGateway", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.100.0"}}, "publicSubnet2": {"id": "publicSubnet2", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1b", "cidrBlock": "********/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/publicSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcpublicSubnet2RouteTableED6AD698"}, "subnetId": {"Ref": "HiViVpcpublicSubnet2Subnet1A126C6E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "routeTableId": {"Ref": "HiViVpcpublicSubnet2RouteTableED6AD698"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.100.0"}}, "privateSubnet1": {"id": "privateSubnet1", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1a", "cidrBlock": "********/24", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "private"}, {"key": "aws-cdk:subnet-type", "value": "Private"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/privateSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/privateSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcprivateSubnet1RouteTableDBC5272E"}, "subnetId": {"Ref": "HiViVpcprivateSubnet1Subnet9F4E7AAD"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "natGatewayId": {"Ref": "HiViVpcpublicSubnet1NATGateway3FE5B7A1"}, "routeTableId": {"Ref": "HiViVpcprivateSubnet1RouteTableDBC5272E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.100.0"}}, "privateSubnet2": {"id": "privateSubnet2", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1b", "cidrBlock": "********/24", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "private"}, {"key": "aws-cdk:subnet-type", "value": "Private"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/privateSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/privateSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcprivateSubnet2RouteTable36BCEE79"}, "subnetId": {"Ref": "HiViVpcprivateSubnet2Subnet1821A4CF"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "natGatewayId": {"Ref": "HiViVpcpublicSubnet1NATGateway3FE5B7A1"}, "routeTableId": {"Ref": "HiViVpcprivateSubnet2RouteTable36BCEE79"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.100.0"}}, "isolatedSubnet1": {"id": "isolatedSubnet1", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1a", "cidrBlock": "********/28", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "isolated"}, {"key": "aws-cdk:subnet-type", "value": "Isolated"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcisolatedSubnet1RouteTable51690248"}, "subnetId": {"Ref": "HiViVpcisolatedSubnet1Subnet7FA1A2BC"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.100.0"}}, "isolatedSubnet2": {"id": "isolatedSubnet2", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2", "children": {"Subnet": {"id": "Subnet", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "us-east-1b", "cidrBlock": "*********/28", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "isolated"}, {"key": "aws-cdk:subnet-type", "value": "Isolated"}, {"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.100.0"}}, "Acl": {"id": "Acl", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "RouteTable": {"id": "RouteTable", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.100.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "HiViVpcisolatedSubnet2RouteTableE6DCD982"}, "subnetId": {"Ref": "HiViVpcisolatedSubnet2Subnet68E365C6"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.100.0"}}, "IGW": {"id": "IGW", "path": "HiViDevelopmentStack/HiViVpc/IGW", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::InternetGateway", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViVpc"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnInternetGateway", "version": "2.100.0"}}, "VPCGW": {"id": "VPCGW", "path": "HiViDevelopmentStack/HiViVpc/VPCGW", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPCGatewayAttachment", "aws:cdk:cloudformation:props": {"internetGatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPCGatewayAttachment", "version": "2.100.0"}}, "RestrictDefaultSecurityGroupCustomResource": {"id": "RestrictDefaultSecurityGroupCustomResource", "path": "HiViDevelopmentStack/HiViVpc/RestrictDefaultSecurityGroupCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "HiViDevelopmentStack/HiViVpc/RestrictDefaultSecurityGroupCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.Vpc", "version": "2.100.0"}}, "Custom::VpcRestrictDefaultSGCustomResourceProvider": {"id": "Custom::VpcRestrictDefaultSGCustomResourceProvider", "path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider", "children": {"Staging": {"id": "Staging", "path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.100.0"}}, "Role": {"id": "Role", "path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.100.0"}}, "Handler": {"id": "Handler", "path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProvider", "version": "2.100.0"}}, "AlbSecurityGroup": {"id": "AlbSecurityGroup", "path": "HiViDevelopmentStack/AlbSecurityGroup", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/AlbSecurityGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Security group for Application Load Balancer", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "securityGroupIngress": [{"cidrIp": "0.0.0.0/0", "ipProtocol": "tcp", "fromPort": 80, "toPort": 80, "description": "Allow HTTP traffic"}, {"cidrIp": "0.0.0.0/0", "ipProtocol": "tcp", "fromPort": 443, "toPort": 443, "description": "Allow HTTPS traffic"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.100.0"}}, "EcsSecurityGroup": {"id": "EcsSecurityGroup", "path": "HiViDevelopmentStack/EcsSecurityGroup", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/EcsSecurityGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Security group for ECS tasks", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.100.0"}}, "from HiViDevelopmentStackAlbSecurityGroupD57596DB:8000": {"id": "from HiViDevelopmentStackAlbSecurityGroupD57596DB:8000", "path": "HiViDevelopmentStack/EcsSecurityGroup/from HiViDevelopmentStackAlbSecurityGroupD57596DB:8000", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupIngress", "aws:cdk:cloudformation:props": {"description": "Allow traffic from ALB", "fromPort": 8000, "groupId": {"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}, "ipProtocol": "tcp", "sourceSecurityGroupId": {"Fn::GetAtt": ["AlbSecurityGroup86A59E99", "GroupId"]}, "toPort": 8000}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupIngress", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.100.0"}}, "RdsSecurityGroup": {"id": "RdsSecurityGroup", "path": "HiViDevelopmentStack/RdsSecurityGroup", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/RdsSecurityGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Security group for RDS database", "securityGroupEgress": [{"cidrIp": "***************/32", "description": "Disallow all traffic", "ipProtocol": "icmp", "fromPort": 252, "toPort": 86}], "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.100.0"}}, "from HiViDevelopmentStackEcsSecurityGroupDE55745A:5432": {"id": "from HiViDevelopmentStackEcsSecurityGroupDE55745A:5432", "path": "HiViDevelopmentStack/RdsSecurityGroup/from HiViDevelopmentStackEcsSecurityGroupDE55745A:5432", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupIngress", "aws:cdk:cloudformation:props": {"description": "Allow PostgreSQL traffic from ECS", "fromPort": 5432, "groupId": {"Fn::GetAtt": ["RdsSecurityGroup632A77E4", "GroupId"]}, "ipProtocol": "tcp", "sourceSecurityGroupId": {"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}, "toPort": 5432}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupIngress", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.100.0"}}, "DatabasePassword": {"id": "DatabasePassword", "path": "HiViDevelopmentStack/DatabasePassword", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/DatabasePassword/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::<PERSON>Manager::Secret", "aws:cdk:cloudformation:props": {"description": "PostgreSQL password for Hi-Vi backend", "generateSecretString": {"secretStringTemplate": "{\"username\":\"hivi_admin\"}", "generateStringKey": "password", "excludeCharacters": "\"@/\\", "passwordLength": 32}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecret", "version": "2.100.0"}}, "Attachment": {"id": "Attachment", "path": "HiViDevelopmentStack/DatabasePassword/Attachment", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/DatabasePassword/Attachment/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SecretsManager::SecretTargetAttachment", "aws:cdk:cloudformation:props": {"secretId": {"Ref": "DatabasePassword49A8070F"}, "targetId": {"Ref": "HiViDatabase96DE84D7"}, "targetType": "AWS::RDS::DBInstance"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecretTargetAttachment", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.SecretTargetAttachment", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.Secret", "version": "2.100.0"}}, "JwtSecret": {"id": "JwtSecret", "path": "HiViDevelopmentStack/JwtSecret", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/JwtSecret/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::<PERSON>Manager::Secret", "aws:cdk:cloudformation:props": {"description": "JWT secret key for Hi-Vi backend", "generateSecretString": {"passwordLength": 64, "excludeCharacters": "\"@/\\"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecret", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.Secret", "version": "2.100.0"}}, "StripeSecret": {"id": "StripeSecret", "path": "HiViDevelopmentStack/StripeSecret", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/StripeSecret/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::<PERSON>Manager::Secret", "aws:cdk:cloudformation:props": {"description": "Stripe secret key for Hi-Vi backend", "secretString": "sk_test_placeholder"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecret", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.Secret", "version": "2.100.0"}}, "HiViDatabase": {"id": "HiViDatabase", "path": "HiViDevelopmentStack/HiViDatabase", "children": {"SubnetGroup": {"id": "SubnetGroup", "path": "HiViDevelopmentStack/HiViDatabase/SubnetGroup", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "HiViDevelopmentStack/HiViDatabase/SubnetGroup/Default", "attributes": {"aws:cdk:cloudformation:type": "AWS::RDS::DBSubnetGroup", "aws:cdk:cloudformation:props": {"dbSubnetGroupDescription": "Subnet group for HiViDatabase database", "subnetIds": [{"Ref": "HiViVpcisolatedSubnet1Subnet7FA1A2BC"}, {"Ref": "HiViVpcisolatedSubnet2Subnet68E365C6"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.CfnDBSubnetGroup", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.SubnetGroup", "version": "2.100.0"}}, "Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViDatabase/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::RDS::DBInstance", "aws:cdk:cloudformation:props": {"allocatedStorage": "20", "backupRetentionPeriod": 7, "copyTagsToSnapshot": true, "dbInstanceClass": "db.t3.small", "dbName": "hivi_production", "dbSubnetGroupName": {"Ref": "HiViDatabaseSubnetGroupBDBE97A0"}, "deletionProtection": false, "engine": "postgres", "engineVersion": "15.4", "masterUsername": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DatabasePassword49A8070F"}, ":SecretString:username::}}"]]}, "masterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DatabasePassword49A8070F"}, ":SecretString:password::}}"]]}, "publiclyAccessible": false, "storageEncrypted": true, "storageType": "gp2", "vpcSecurityGroups": [{"Fn::GetAtt": ["RdsSecurityGroup632A77E4", "GroupId"]}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.CfnDBInstance", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.DatabaseInstance", "version": "2.100.0"}}, "HiViBackendRepository": {"id": "HiViBackendRepository", "path": "HiViDevelopmentStack/HiViBackendRepository", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViBackendRepository/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECR::Repository", "aws:cdk:cloudformation:props": {"imageScanningConfiguration": {"scanOnPush": true}, "lifecyclePolicy": {"lifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"untagged\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}},{\"rulePriority\":2,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"sinceImagePushed\",\"countNumber\":30,\"countUnit\":\"days\"},\"action\":{\"type\":\"expire\"}}]}"}, "repositoryName": "hivi-backend-development"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.CfnRepository", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.Repository", "version": "2.100.0"}}, "HiViCluster": {"id": "HiViCluster", "path": "HiViDevelopmentStack/HiViCluster", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViCluster/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Cluster", "aws:cdk:cloudformation:props": {"clusterName": "hivi-cluster", "clusterSettings": [{"name": "containerInsights", "value": "disabled"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnCluster", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.Cluster", "version": "2.100.0"}}, "HiViLoadBalancer": {"id": "HiViLoadBalancer", "path": "HiViDevelopmentStack/HiViLoadBalancer", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViLoadBalancer/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "aws:cdk:cloudformation:props": {"loadBalancerAttributes": [{"key": "deletion_protection.enabled", "value": "false"}], "scheme": "internet-facing", "securityGroups": [{"Fn::GetAtt": ["AlbSecurityGroup86A59E99", "GroupId"]}], "subnets": [{"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}, {"Ref": "HiViVpcpublicSubnet2Subnet1A126C6E"}], "type": "application"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnLoadBalancer", "version": "2.100.0"}}, "HttpsListener": {"id": "HttpsListener", "path": "HiViDevelopmentStack/HiViLoadBalancer/HttpsListener", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViLoadBalancer/HttpsListener/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::Listener", "aws:cdk:cloudformation:props": {"certificates": [{"certificateArn": {"Ref": "HiViCertificate0F67EDAB"}}], "defaultActions": [{"type": "forward", "targetGroupArn": {"Ref": "HiViTargetGroup83A7AC09"}}], "loadBalancerArn": {"Ref": "HiViLoadBalancer0C0CF18D"}, "port": 443, "protocol": "HTTPS"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnListener", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationListener", "version": "2.100.0"}}, "HttpListener": {"id": "HttpListener", "path": "HiViDevelopmentStack/HiViLoadBalancer/HttpListener", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViLoadBalancer/HttpListener/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::Listener", "aws:cdk:cloudformation:props": {"defaultActions": [{"type": "redirect", "redirectConfig": {"statusCode": "HTTP_301", "port": "443", "protocol": "HTTPS"}}], "loadBalancerArn": {"Ref": "HiViLoadBalancer0C0CF18D"}, "port": 80, "protocol": "HTTP"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnListener", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationListener", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationLoadBalancer", "version": "2.100.0"}}, "HiViHostedZone": {"id": "HiViHostedZone", "path": "HiViDevelopmentStack/HiViHostedZone", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViHostedZone/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Route53::HostedZone", "aws:cdk:cloudformation:props": {"name": "dev.hi-vi.com."}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_route53.CfnHostedZone", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_route53.HostedZone", "version": "2.100.0"}}, "HiViCertificate": {"id": "HiViCertificate", "path": "HiViDevelopmentStack/HiViCertificate", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViCertificate/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CertificateManager::Certificate", "aws:cdk:cloudformation:props": {"domainName": "api.dev.hi-vi.com", "domainValidationOptions": [{"domainName": "api.dev.hi-vi.com", "hostedZoneId": {"Ref": "HiViHostedZoneE404FAD8"}}], "tags": [{"key": "Name", "value": "HiViDevelopmentStack/HiViCertificate"}], "validationMethod": "DNS"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_certificatemanager.CfnCertificate", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_certificatemanager.Certificate", "version": "2.100.0"}}, "ApiAliasRecord": {"id": "ApiAliasRecord", "path": "HiViDevelopmentStack/ApiAliasRecord", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/ApiAliasRecord/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Route53::RecordSet", "aws:cdk:cloudformation:props": {"aliasTarget": {"hostedZoneId": {"Fn::GetAtt": ["HiViLoadBalancer0C0CF18D", "CanonicalHostedZoneID"]}, "dnsName": {"Fn::Join": ["", ["dualstack.", {"Fn::GetAtt": ["HiViLoadBalancer0C0CF18D", "DNSName"]}]]}}, "hostedZoneId": {"Ref": "HiViHostedZoneE404FAD8"}, "name": "api.dev.hi-vi.com.", "type": "A"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_route53.CfnRecordSet", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_route53.ARecord", "version": "2.100.0"}}, "HiViTargetGroup": {"id": "HiViTargetGroup", "path": "HiViDevelopmentStack/HiViTargetGroup", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTargetGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::TargetGroup", "aws:cdk:cloudformation:props": {"healthCheckEnabled": true, "healthCheckIntervalSeconds": 30, "healthCheckPath": "/api/v1/utils/health-check/", "healthCheckProtocol": "HTTP", "healthCheckTimeoutSeconds": 5, "healthyThresholdCount": 2, "matcher": {"httpCode": "200"}, "port": 8000, "protocol": "HTTP", "targetGroupAttributes": [{"key": "stickiness.enabled", "value": "false"}], "targetType": "ip", "unhealthyThresholdCount": 3, "vpcId": {"Ref": "HiViVpcFDF33547"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnTargetGroup", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationTargetGroup", "version": "2.100.0"}}, "HiViLogGroup": {"id": "HiViLogGroup", "path": "HiViDevelopmentStack/HiViLogGroup", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViLogGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": "/ecs/hivi-backend", "retentionInDays": 7}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.100.0"}}, "HiViTaskDefinition": {"id": "HiViTaskDefinition", "path": "HiViDevelopmentStack/HiViTaskDefinition", "children": {"TaskRole": {"id": "TaskRole", "path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole", "children": {"ImportTaskRole": {"id": "ImportTaskRole", "path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/ImportTaskRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.100.0"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": [{"Ref": "DatabasePassword49A8070F"}, {"Ref": "JwtSecretB8834B39"}, {"Ref": "StripeSecret80A38A68"}]}], "Version": "2012-10-17"}, "policyName": "HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "roles": [{"Ref": "HiViTaskDefinitionTaskRole684C2C51"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.100.0"}}, "Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTaskDefinition/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::TaskDefinition", "aws:cdk:cloudformation:props": {"containerDefinitions": [{"essential": true, "image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "HiViBackendRepositoryD192C98A"}, ":latest"]]}, "name": "HiViBackendContainer", "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": {"Ref": "HiViLogGroupB9322250"}, "awslogs-stream-prefix": "ecs", "awslogs-region": "us-east-1"}}, "environment": [{"name": "ENVIRONMENT", "value": "development"}, {"name": "PROJECT_NAME", "value": "Hi-Vi Spiritual Coaching Platform"}, {"name": "API_V1_STR", "value": "/api/v1"}, {"name": "POSTGRES_SERVER", "value": {"Fn::GetAtt": ["HiViDatabase96DE84D7", "Endpoint.Address"]}}, {"name": "POSTGRES_PORT", "value": "5432"}, {"name": "POSTGRES_USER", "value": "hivi_admin"}, {"name": "POSTGRES_DB", "value": "hivi_production"}, {"name": "BACKEND_CORS_ORIGINS", "value": "[\"https://dashboard.dev.hi-vi.com\",\"https://dev.hi-vi.com\"]"}, {"name": "FRONTEND_HOST", "value": "https://dashboard.dev.hi-vi.com"}], "secrets": [{"name": "POSTGRES_PASSWORD", "valueFrom": {"Fn::Join": ["", [{"Ref": "DatabasePassword49A8070F"}, ":password::"]]}}, {"name": "SECRET_KEY", "valueFrom": {"Ref": "JwtSecretB8834B39"}}, {"name": "STRIPE_SECRET_KEY", "valueFrom": {"Ref": "StripeSecret80A38A68"}}], "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/utils/health-check/ || exit 1"], "interval": 30, "retries": 3, "startPeriod": 60, "timeout": 5}}], "cpu": "512", "executionRoleArn": {"Fn::GetAtt": ["HiViTaskDefinitionExecutionRole815CDE10", "<PERSON><PERSON>"]}, "family": "HiViDevelopmentStackHiViTaskDefinition878CB736", "memory": "1024", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "taskRoleArn": {"Fn::GetAtt": ["HiViTaskDefinitionTaskRole684C2C51", "<PERSON><PERSON>"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnTaskDefinition", "version": "2.100.0"}}, "HiViBackendContainer": {"id": "HiViBackendContainer", "path": "HiViDevelopmentStack/HiViTaskDefinition/HiViBackendContainer", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ContainerDefinition", "version": "2.100.0"}}, "ExecutionRole": {"id": "ExecutionRole", "path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole", "children": {"ImportExecutionRole": {"id": "ImportExecutionRole", "path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/ImportExecutionRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.100.0"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["HiViLogGroupB9322250", "<PERSON><PERSON>"]}}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": [{"Ref": "DatabasePassword49A8070F"}, {"Ref": "JwtSecretB8834B39"}, {"Ref": "StripeSecret80A38A68"}]}], "Version": "2012-10-17"}, "policyName": "HiViTaskDefinitionExecutionRoleDefaultPolicyBBB2E439", "roles": [{"Ref": "HiViTaskDefinitionExecutionRole815CDE10"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateTaskDefinition", "version": "2.100.0"}}, "HiViBackendService": {"id": "HiViBackendService", "path": "HiViDevelopmentStack/HiViBackendService", "children": {"Service": {"id": "Service", "path": "HiViDevelopmentStack/HiViBackendService/Service", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Service", "aws:cdk:cloudformation:props": {"cluster": {"Ref": "HiViCluster5D213706"}, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "alarms": {"alarmNames": [], "enable": false, "rollback": false}}, "desiredCount": 1, "enableEcsManagedTags": false, "healthCheckGracePeriodSeconds": 300, "launchType": "FARGATE", "loadBalancers": [{"targetGroupArn": {"Ref": "HiViTargetGroup83A7AC09"}, "containerName": "HiViBackendContainer", "containerPort": 8000}], "networkConfiguration": {"awsvpcConfiguration": {"assignPublicIp": "DISABLED", "subnets": [{"Ref": "HiViVpcprivateSubnet1Subnet9F4E7AAD"}, {"Ref": "HiViVpcprivateSubnet2Subnet1821A4CF"}], "securityGroups": [{"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}]}}, "serviceName": "hivi-backend", "taskDefinition": {"Ref": "HiViTaskDefinitionE4DE095E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnService", "version": "2.100.0"}}, "ScalingRole": {"id": "ScalingRole", "path": "HiViDevelopmentStack/HiViBackendService/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.100.0"}}, "TaskCount": {"id": "TaskCount", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount", "children": {"Target": {"id": "Target", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalableTarget", "aws:cdk:cloudformation:props": {"maxCapacity": 3, "minCapacity": 1, "resourceId": {"Fn::Join": ["", ["service/", {"Ref": "HiViCluster5D213706"}, "/", {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}]]}, "roleArn": "arn:aws:iam::787992550718:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "scalableDimension": "ecs:service:DesiredCount", "serviceNamespace": "ecs"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalableTarget", "version": "2.100.0"}}, "CpuScaling": {"id": "CpuScaling", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/CpuScaling", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/CpuScaling/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalingPolicy", "aws:cdk:cloudformation:props": {"policyName": "HiViDevelopmentStackHiViBackendServiceTaskCountTargetCpuScaling3942098A", "policyType": "TargetTrackingScaling", "scalingTargetId": {"Ref": "HiViBackendServiceTaskCountTarget6C48E879"}, "targetTrackingScalingPolicyConfiguration": {"predefinedMetricSpecification": {"predefinedMetricType": "ECSServiceAverageCPUUtilization"}, "scaleInCooldown": 300, "scaleOutCooldown": 120, "targetValue": 70}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalingPolicy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.TargetTrackingScalingPolicy", "version": "2.100.0"}}, "MemoryScaling": {"id": "MemoryScaling", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/MemoryScaling", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/MemoryScaling/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalingPolicy", "aws:cdk:cloudformation:props": {"policyName": "HiViDevelopmentStackHiViBackendServiceTaskCountTargetMemoryScalingDD5F2D99", "policyType": "TargetTrackingScaling", "scalingTargetId": {"Ref": "HiViBackendServiceTaskCountTarget6C48E879"}, "targetTrackingScalingPolicyConfiguration": {"predefinedMetricSpecification": {"predefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "scaleInCooldown": 300, "scaleOutCooldown": 120, "targetValue": 80}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalingPolicy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.TargetTrackingScalingPolicy", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.ScalableTarget", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ScalableTaskCount", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateService", "version": "2.100.0"}}, "HighCpuAlarm": {"id": "HighCpuAlarm", "path": "HiViDevelopmentStack/HighCpuAlarm", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HighCpuAlarm/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "ClusterName", "value": {"Ref": "HiViCluster5D213706"}}, {"name": "ServiceName", "value": {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}}], "evaluationPeriods": 2, "metricName": "CPUUtilization", "namespace": "AWS/ECS", "period": 300, "statistic": "Average", "threshold": 80, "treatMissingData": "notBreaching"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.100.0"}}, "HighMemoryAlarm": {"id": "HighMemoryAlarm", "path": "HiViDevelopmentStack/HighMemoryAlarm", "children": {"Resource": {"id": "Resource", "path": "HiViDevelopmentStack/HighMemoryAlarm/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "ClusterName", "value": {"Ref": "HiViCluster5D213706"}}, {"name": "ServiceName", "value": {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}}], "evaluationPeriods": 2, "metricName": "MemoryUtilization", "namespace": "AWS/ECS", "period": 300, "statistic": "Average", "threshold": 85, "treatMissingData": "notBreaching"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.100.0"}}, "LoadBalancerDNS": {"id": "LoadBalancerDNS", "path": "HiViDevelopmentStack/LoadBalancerDNS", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.100.0"}}, "DatabaseEndpoint": {"id": "DatabaseEndpoint", "path": "HiViDevelopmentStack/DatabaseEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.100.0"}}, "ECRRepositoryURI": {"id": "ECRRepositoryURI", "path": "HiViDevelopmentStack/ECRRepositoryURI", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.100.0"}}, "ApiUrl": {"id": "ApiUrl", "path": "HiViDevelopmentStack/ApiUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.100.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "HiViDevelopmentStack/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "HiViDevelopmentStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.100.0"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "HiViDevelopmentStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.100.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "HiViDevelopmentStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.100.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.100.0"}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.100.0"}}}