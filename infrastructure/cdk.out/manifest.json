{"version": "34.0.0", "artifacts": {"HiViDevelopmentStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "HiViDevelopmentStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "HiViDevelopmentStack": {"type": "aws:cloudformation:stack", "environment": "aws://787992550718/us-east-1", "properties": {"templateFile": "HiViDevelopmentStack.template.json", "terminationProtection": false, "tags": {"Environment": "development", "Owner": "Development Team", "Project": "Hi-Vi"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::787992550718:role/cdk-hnb659fds-deploy-role-787992550718-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::787992550718:role/cdk-hnb659fds-cfn-exec-role-787992550718-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-787992550718-us-east-1/49357ef25f5a6c85cb195cc45aebcc230c9161e5cb77704045389a1984333110.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["HiViDevelopmentStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::787992550718:role/cdk-hnb659fds-lookup-role-787992550718-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}, "stackName": "hivi-development"}, "dependencies": ["HiViDevelopmentStack.assets"], "metadata": {"/HiViDevelopmentStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "development"}, {"Key": "Owner", "Value": "Development Team"}, {"Key": "Project", "Value": "Hi-Vi"}]}], "/HiViDevelopmentStack/HiViVpc/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViVpcFDF33547"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1RouteTable4BBFB7E9"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1RouteTableAssociation4991E56F"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1DefaultRouteD0319B4E"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/EIP": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1EIPAC015373"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet1/NATGateway": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet1NATGateway3FE5B7A1"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet2Subnet1A126C6E"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet2RouteTableED6AD698"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet2RouteTableAssociation1EC0904D"}], "/HiViDevelopmentStack/HiViVpc/publicSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "HiViVpcpublicSubnet2DefaultRoute0A8E0C78"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet1Subnet9F4E7AAD"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet1RouteTableDBC5272E"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet1RouteTableAssociationA8C2AFEE"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet1DefaultRouteC014605B"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet2Subnet1821A4CF"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet2RouteTable36BCEE79"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet2RouteTableAssociation18225D8E"}], "/HiViDevelopmentStack/HiViVpc/privateSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "HiViVpcprivateSubnet2DefaultRoute594DF7F2"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet1Subnet7FA1A2BC"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet1RouteTable51690248"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet1RouteTableAssociationC87F3D4D"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet2Subnet68E365C6"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet2RouteTableE6DCD982"}], "/HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "HiViVpcisolatedSubnet2RouteTableAssociation27762D91"}], "/HiViDevelopmentStack/HiViVpc/IGW": [{"type": "aws:cdk:logicalId", "data": "HiViVpcIGW96678D1E"}], "/HiViDevelopmentStack/HiViVpc/VPCGW": [{"type": "aws:cdk:logicalId", "data": "HiViVpcVPCGW05AF1D88"}], "/HiViDevelopmentStack/HiViVpc/RestrictDefaultSecurityGroupCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "HiViVpcRestrictDefaultSecurityGroupCustomResource598690FB"}], "/HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"}], "/HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E"}], "/HiViDevelopmentStack/AlbSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "AlbSecurityGroup86A59E99"}], "/HiViDevelopmentStack/EcsSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "EcsSecurityGroup44008BF1"}], "/HiViDevelopmentStack/EcsSecurityGroup/from HiViDevelopmentStackAlbSecurityGroupD57596DB:8000": [{"type": "aws:cdk:logicalId", "data": "EcsSecurityGroupfromHiViDevelopmentStackAlbSecurityGroupD57596DB80002D73902B"}], "/HiViDevelopmentStack/RdsSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "RdsSecurityGroup632A77E4"}], "/HiViDevelopmentStack/RdsSecurityGroup/from HiViDevelopmentStackEcsSecurityGroupDE55745A:5432": [{"type": "aws:cdk:logicalId", "data": "RdsSecurityGroupfromHiViDevelopmentStackEcsSecurityGroupDE55745A5432453F49E0"}], "/HiViDevelopmentStack/DatabasePassword/Resource": [{"type": "aws:cdk:logicalId", "data": "DatabasePassword49A8070F"}], "/HiViDevelopmentStack/DatabasePassword/Attachment/Resource": [{"type": "aws:cdk:logicalId", "data": "DatabasePasswordAttachment4378D145"}], "/HiViDevelopmentStack/JwtSecret/Resource": [{"type": "aws:cdk:logicalId", "data": "JwtSecretB8834B39"}], "/HiViDevelopmentStack/StripeSecret/Resource": [{"type": "aws:cdk:logicalId", "data": "StripeSecret80A38A68"}], "/HiViDevelopmentStack/HiViDatabase/SubnetGroup/Default": [{"type": "aws:cdk:logicalId", "data": "HiViDatabaseSubnetGroupBDBE97A0"}], "/HiViDevelopmentStack/HiViDatabase/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViDatabase96DE84D7"}], "/HiViDevelopmentStack/HiViBackendRepository/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViBackendRepositoryD192C98A"}], "/HiViDevelopmentStack/HiViCluster/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViCluster5D213706"}], "/HiViDevelopmentStack/HiViLoadBalancer/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViLoadBalancer0C0CF18D"}], "/HiViDevelopmentStack/HiViLoadBalancer/HttpsListener/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViLoadBalancerHttpsListenerD1951CDE"}], "/HiViDevelopmentStack/HiViLoadBalancer/HttpListener/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViLoadBalancerHttpListener2AD33BE9"}], "/HiViDevelopmentStack/HiViHostedZone/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViHostedZoneE404FAD8"}], "/HiViDevelopmentStack/HiViCertificate/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViCertificate0F67EDAB"}], "/HiViDevelopmentStack/ApiAliasRecord/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiAliasRecordBCE933A3"}], "/HiViDevelopmentStack/HiViTargetGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTargetGroup83A7AC09"}], "/HiViDevelopmentStack/HiViLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViLogGroupB9322250"}], "/HiViDevelopmentStack/HiViTaskDefinition/TaskRole/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTaskDefinitionTaskRole684C2C51"}], "/HiViDevelopmentStack/HiViTaskDefinition/TaskRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB"}], "/HiViDevelopmentStack/HiViTaskDefinition/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTaskDefinitionE4DE095E"}], "/HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTaskDefinitionExecutionRole815CDE10"}], "/HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViTaskDefinitionExecutionRoleDefaultPolicyBBB2E439"}], "/HiViDevelopmentStack/HiViBackendService/Service": [{"type": "aws:cdk:logicalId", "data": "HiViBackendService53A539D4"}], "/HiViDevelopmentStack/HiViBackendService/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViBackendServiceTaskCountTarget6C48E879"}], "/HiViDevelopmentStack/HiViBackendService/TaskCount/Target/CpuScaling/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViBackendServiceTaskCountTargetCpuScaling8BBB29A9"}], "/HiViDevelopmentStack/HiViBackendService/TaskCount/Target/MemoryScaling/Resource": [{"type": "aws:cdk:logicalId", "data": "HiViBackendServiceTaskCountTargetMemoryScaling7DAC75E4"}], "/HiViDevelopmentStack/HighCpuAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "HighCpuAlarm40723000"}], "/HiViDevelopmentStack/HighMemoryAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "HighMemoryAlarmB77DAB20"}], "/HiViDevelopmentStack/LoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "LoadBalancerDNS"}], "/HiViDevelopmentStack/DatabaseEndpoint": [{"type": "aws:cdk:logicalId", "data": "DatabaseEndpoint"}], "/HiViDevelopmentStack/ECRRepositoryURI": [{"type": "aws:cdk:logicalId", "data": "ECRRepositoryURI"}], "/HiViDevelopmentStack/ApiUrl": [{"type": "aws:cdk:logicalId", "data": "ApiUrl"}], "/HiViDevelopmentStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/HiViDevelopmentStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/HiViDevelopmentStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "HiViDevelopmentStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}}