{"Description": "Hi-Vi Spiritual Coaching Platform - Development Environment", "Resources": {"HiViVpcFDF33547": {"Type": "AWS::EC2::VPC", "Properties": {"CidrBlock": "10.0.0.0/16", "EnableDnsHostnames": true, "EnableDnsSupport": true, "InstanceTenancy": "default", "Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/Resource"}}, "HiViVpcpublicSubnet1Subnet2DE1ABCE": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "10.0.0.0/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/Subnet"}}, "HiViVpcpublicSubnet1RouteTable4BBFB7E9": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTable"}}, "HiViVpcpublicSubnet1RouteTableAssociation4991E56F": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcpublicSubnet1RouteTable4BBFB7E9"}, "SubnetId": {"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/RouteTableAssociation"}}, "HiViVpcpublicSubnet1DefaultRouteD0319B4E": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "RouteTableId": {"Ref": "HiViVpcpublicSubnet1RouteTable4BBFB7E9"}}, "DependsOn": ["HiViVpcVPCGW05AF1D88"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/DefaultRoute"}}, "HiViVpcpublicSubnet1EIPAC015373": {"Type": "AWS::EC2::EIP", "Properties": {"Domain": "vpc", "Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/EIP"}}, "HiViVpcpublicSubnet1NATGateway3FE5B7A1": {"Type": "AWS::EC2::NatGateway", "Properties": {"AllocationId": {"Fn::GetAtt": ["HiViVpcpublicSubnet1EIPAC015373", "AllocationId"]}, "SubnetId": {"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}, "Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet1"}]}, "DependsOn": ["HiViVpcpublicSubnet1DefaultRouteD0319B4E", "HiViVpcpublicSubnet1RouteTableAssociation4991E56F"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet1/NATGateway"}}, "HiViVpcpublicSubnet2Subnet1A126C6E": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/Subnet"}}, "HiViVpcpublicSubnet2RouteTableED6AD698": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/publicSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTable"}}, "HiViVpcpublicSubnet2RouteTableAssociation1EC0904D": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcpublicSubnet2RouteTableED6AD698"}, "SubnetId": {"Ref": "HiViVpcpublicSubnet2Subnet1A126C6E"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/RouteTableAssociation"}}, "HiViVpcpublicSubnet2DefaultRoute0A8E0C78": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "RouteTableId": {"Ref": "HiViVpcpublicSubnet2RouteTableED6AD698"}}, "DependsOn": ["HiViVpcVPCGW05AF1D88"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/publicSubnet2/DefaultRoute"}}, "HiViVpcprivateSubnet1Subnet9F4E7AAD": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/privateSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/Subnet"}}, "HiViVpcprivateSubnet1RouteTableDBC5272E": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/privateSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTable"}}, "HiViVpcprivateSubnet1RouteTableAssociationA8C2AFEE": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcprivateSubnet1RouteTableDBC5272E"}, "SubnetId": {"Ref": "HiViVpcprivateSubnet1Subnet9F4E7AAD"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/RouteTableAssociation"}}, "HiViVpcprivateSubnet1DefaultRouteC014605B": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "HiViVpcpublicSubnet1NATGateway3FE5B7A1"}, "RouteTableId": {"Ref": "HiViVpcprivateSubnet1RouteTableDBC5272E"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet1/DefaultRoute"}}, "HiViVpcprivateSubnet2Subnet1821A4CF": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/privateSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/Subnet"}}, "HiViVpcprivateSubnet2RouteTable36BCEE79": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/privateSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTable"}}, "HiViVpcprivateSubnet2RouteTableAssociation18225D8E": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcprivateSubnet2RouteTable36BCEE79"}, "SubnetId": {"Ref": "HiViVpcprivateSubnet2Subnet1821A4CF"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/RouteTableAssociation"}}, "HiViVpcprivateSubnet2DefaultRoute594DF7F2": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "HiViVpcpublicSubnet1NATGateway3FE5B7A1"}, "RouteTableId": {"Ref": "HiViVpcprivateSubnet2RouteTable36BCEE79"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/privateSubnet2/DefaultRoute"}}, "HiViVpcisolatedSubnet1Subnet7FA1A2BC": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1a", "CidrBlock": "********/28", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "isolated"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/Subnet"}}, "HiViVpcisolatedSubnet1RouteTable51690248": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTable"}}, "HiViVpcisolatedSubnet1RouteTableAssociationC87F3D4D": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcisolatedSubnet1RouteTable51690248"}, "SubnetId": {"Ref": "HiViVpcisolatedSubnet1Subnet7FA1A2BC"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet1/RouteTableAssociation"}}, "HiViVpcisolatedSubnet2Subnet68E365C6": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "us-east-1b", "CidrBlock": "*********/28", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "isolated"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/Subnet"}}, "HiViVpcisolatedSubnet2RouteTableE6DCD982": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTable"}}, "HiViVpcisolatedSubnet2RouteTableAssociation27762D91": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "HiViVpcisolatedSubnet2RouteTableE6DCD982"}, "SubnetId": {"Ref": "HiViVpcisolatedSubnet2Subnet68E365C6"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/isolatedSubnet2/RouteTableAssociation"}}, "HiViVpcIGW96678D1E": {"Type": "AWS::EC2::InternetGateway", "Properties": {"Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViVpc"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/IGW"}}, "HiViVpcVPCGW05AF1D88": {"Type": "AWS::EC2::VPCGatewayAttachment", "Properties": {"InternetGatewayId": {"Ref": "HiViVpcIGW96678D1E"}, "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/VPCGW"}}, "HiViVpcRestrictDefaultSecurityGroupCustomResource598690FB": {"Type": "Custom::VpcRestrictDefaultSG", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E", "<PERSON><PERSON>"]}, "DefaultSecurityGroupId": {"Fn::GetAtt": ["HiViVpcFDF33547", "DefaultSecurityGroup"]}, "Account": "************"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViVpc/RestrictDefaultSecurityGroupCustomResource/Default"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}], "Policies": [{"PolicyName": "Inline", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:AuthorizeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ec2:RevokeSecurityGroupEgress"], "Resource": [{"Fn::Join": ["", ["arn:aws:ec2:us-east-1:************:security-group/", {"Fn::GetAtt": ["HiViVpcFDF33547", "DefaultSecurityGroup"]}]]}]}]}}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-************-us-east-1", "S3Key": "7f18a11296f35510ee16538afec983ed6312e12afbf81b777089a9f8e34e2474.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "__entrypoint__.handler", "Role": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Description": "Lambda function for removing all inbound/outbound rules from the VPC default security group"}, "DependsOn": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "aws:asset:path": "asset.7f18a11296f35510ee16538afec983ed6312e12afbf81b777089a9f8e34e2474", "aws:asset:property": "Code"}}, "AlbSecurityGroup86A59E99": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for Application Load Balancer", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "SecurityGroupIngress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow HTTP traffic", "FromPort": 80, "IpProtocol": "tcp", "ToPort": 80}, {"CidrIp": "0.0.0.0/0", "Description": "Allow HTTPS traffic", "FromPort": 443, "IpProtocol": "tcp", "ToPort": 443}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/AlbSecurityGroup/Resource"}}, "EcsSecurityGroup44008BF1": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for ECS tasks", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/EcsSecurityGroup/Resource"}}, "EcsSecurityGroupfromHiViDevelopmentStackAlbSecurityGroupD57596DB80002D73902B": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow traffic from ALB", "FromPort": 8000, "GroupId": {"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["AlbSecurityGroup86A59E99", "GroupId"]}, "ToPort": 8000}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/EcsSecurityGroup/from HiViDevelopmentStackAlbSecurityGroupD57596DB:8000"}}, "RdsSecurityGroup632A77E4": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for RDS database", "SecurityGroupEgress": [{"CidrIp": "***************/32", "Description": "Disallow all traffic", "FromPort": 252, "IpProtocol": "icmp", "ToPort": 86}], "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/RdsSecurityGroup/Resource"}}, "RdsSecurityGroupfromHiViDevelopmentStackEcsSecurityGroupDE55745A5432453F49E0": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Allow PostgreSQL traffic from ECS", "FromPort": 5432, "GroupId": {"Fn::GetAtt": ["RdsSecurityGroup632A77E4", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}, "ToPort": 5432}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/RdsSecurityGroup/from HiViDevelopmentStackEcsSecurityGroupDE55745A:5432"}}, "DatabasePassword49A8070F": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": "PostgreSQL password for Hi-Vi backend", "GenerateSecretString": {"ExcludeCharacters": "\"@/\\", "GenerateStringKey": "password", "PasswordLength": 32, "SecretStringTemplate": "{\"username\":\"hivi_admin\"}"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/DatabasePassword/Resource"}}, "DatabasePasswordAttachment4378D145": {"Type": "AWS::SecretsManager::SecretTargetAttachment", "Properties": {"SecretId": {"Ref": "DatabasePassword49A8070F"}, "TargetId": {"Ref": "HiViDatabase96DE84D7"}, "TargetType": "AWS::RDS::DBInstance"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/DatabasePassword/Attachment/Resource"}}, "JwtSecretB8834B39": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": "JWT secret key for Hi-Vi backend", "GenerateSecretString": {"ExcludeCharacters": "\"@/\\", "PasswordLength": 64}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/JwtSecret/Resource"}}, "StripeSecret80A38A68": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": "Stripe secret key for Hi-Vi backend", "SecretString": "sk_test_placeholder"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/StripeSecret/Resource"}}, "HiViDatabaseSubnetGroupBDBE97A0": {"Type": "AWS::RDS::DBSubnetGroup", "Properties": {"DBSubnetGroupDescription": "Subnet group for HiViDatabase database", "SubnetIds": [{"Ref": "HiViVpcisolatedSubnet1Subnet7FA1A2BC"}, {"Ref": "HiViVpcisolatedSubnet2Subnet68E365C6"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViDatabase/SubnetGroup/Default"}}, "HiViDatabase96DE84D7": {"Type": "AWS::RDS::DBInstance", "Properties": {"AllocatedStorage": "20", "BackupRetentionPeriod": 7, "CopyTagsToSnapshot": true, "DBInstanceClass": "db.t3.small", "DBName": "hivi_production", "DBSubnetGroupName": {"Ref": "HiViDatabaseSubnetGroupBDBE97A0"}, "DeletionProtection": false, "Engine": "postgres", "EngineVersion": "12", "MasterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DatabasePassword49A8070F"}, ":SecretString:password::}}"]]}, "MasterUsername": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "DatabasePassword49A8070F"}, ":SecretString:username::}}"]]}, "PubliclyAccessible": false, "StorageEncrypted": true, "StorageType": "gp2", "VPCSecurityGroups": [{"Fn::GetAtt": ["RdsSecurityGroup632A77E4", "GroupId"]}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViDatabase/Resource"}}, "HiViBackendRepositoryD192C98A": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"untagged\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}},{\"rulePriority\":2,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"sinceImagePushed\",\"countNumber\":30,\"countUnit\":\"days\"},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "hivi-backend-development"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViBackendRepository/Resource"}}, "HiViCluster5D213706": {"Type": "AWS::ECS::Cluster", "Properties": {"ClusterName": "hivi-cluster", "ClusterSettings": [{"Name": "containerInsights", "Value": "disabled"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViCluster/Resource"}}, "HiViLoadBalancer0C0CF18D": {"Type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "Properties": {"LoadBalancerAttributes": [{"Key": "deletion_protection.enabled", "Value": "false"}], "Scheme": "internet-facing", "SecurityGroups": [{"Fn::GetAtt": ["AlbSecurityGroup86A59E99", "GroupId"]}], "Subnets": [{"Ref": "HiViVpcpublicSubnet1Subnet2DE1ABCE"}, {"Ref": "HiViVpcpublicSubnet2Subnet1A126C6E"}], "Type": "application"}, "DependsOn": ["HiViVpcpublicSubnet1DefaultRouteD0319B4E", "HiViVpcpublicSubnet1RouteTableAssociation4991E56F", "HiViVpcpublicSubnet2DefaultRoute0A8E0C78", "HiViVpcpublicSubnet2RouteTableAssociation1EC0904D"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViLoadBalancer/Resource"}}, "HiViLoadBalancerHttpsListenerD1951CDE": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"Certificates": [{"CertificateArn": {"Ref": "HiViCertificate0F67EDAB"}}], "DefaultActions": [{"TargetGroupArn": {"Ref": "HiViTargetGroup83A7AC09"}, "Type": "forward"}], "LoadBalancerArn": {"Ref": "HiViLoadBalancer0C0CF18D"}, "Port": 443, "Protocol": "HTTPS"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViLoadBalancer/HttpsListener/Resource"}}, "HiViLoadBalancerHttpListener2AD33BE9": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"DefaultActions": [{"RedirectConfig": {"Port": "443", "Protocol": "HTTPS", "StatusCode": "HTTP_301"}, "Type": "redirect"}], "LoadBalancerArn": {"Ref": "HiViLoadBalancer0C0CF18D"}, "Port": 80, "Protocol": "HTTP"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViLoadBalancer/HttpListener/Resource"}}, "HiViHostedZoneE404FAD8": {"Type": "AWS::Route53::HostedZone", "Properties": {"Name": "dev.hi-vi.com."}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViHostedZone/Resource"}}, "HiViCertificate0F67EDAB": {"Type": "AWS::CertificateManager::Certificate", "Properties": {"DomainName": "api.dev.hi-vi.com", "DomainValidationOptions": [{"DomainName": "api.dev.hi-vi.com", "HostedZoneId": {"Ref": "HiViHostedZoneE404FAD8"}}], "Tags": [{"Key": "Name", "Value": "HiViDevelopmentStack/HiViCertificate"}], "ValidationMethod": "DNS"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViCertificate/Resource"}}, "ApiAliasRecordBCE933A3": {"Type": "AWS::Route53::RecordSet", "Properties": {"AliasTarget": {"DNSName": {"Fn::Join": ["", ["dualstack.", {"Fn::GetAtt": ["HiViLoadBalancer0C0CF18D", "DNSName"]}]]}, "HostedZoneId": {"Fn::GetAtt": ["HiViLoadBalancer0C0CF18D", "CanonicalHostedZoneID"]}}, "HostedZoneId": {"Ref": "HiViHostedZoneE404FAD8"}, "Name": "api.dev.hi-vi.com.", "Type": "A"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/ApiAliasRecord/Resource"}}, "HiViTargetGroup83A7AC09": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckEnabled": true, "HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/api/v1/utils/health-check/", "HealthCheckProtocol": "HTTP", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 2, "Matcher": {"HttpCode": "200"}, "Port": 8000, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Ref": "HiViVpcFDF33547"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTargetGroup/Resource"}}, "HiViLogGroupB9322250": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/ecs/hivi-backend", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViLogGroup/Resource"}}, "HiViTaskDefinitionTaskRole684C2C51": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/Resource"}}, "HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": [{"Ref": "DatabasePassword49A8070F"}, {"Ref": "JwtSecretB8834B39"}, {"Ref": "StripeSecret80A38A68"}]}], "Version": "2012-10-17"}, "PolicyName": "HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "Roles": [{"Ref": "HiViTaskDefinitionTaskRole684C2C51"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTaskDefinition/TaskRole/DefaultPolicy/Resource"}}, "HiViTaskDefinitionE4DE095E": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "ENVIRONMENT", "Value": "development"}, {"Name": "PROJECT_NAME", "Value": "Hi-Vi Spiritual Coaching Platform"}, {"Name": "API_V1_STR", "Value": "/api/v1"}, {"Name": "POSTGRES_SERVER", "Value": {"Fn::GetAtt": ["HiViDatabase96DE84D7", "Endpoint.Address"]}}, {"Name": "POSTGRES_PORT", "Value": "5432"}, {"Name": "POSTGRES_USER", "Value": "hivi_admin"}, {"Name": "POSTGRES_DB", "Value": "hivi_production"}, {"Name": "BACKEND_CORS_ORIGINS", "Value": "[\"https://dashboard.dev.hi-vi.com\",\"https://dev.hi-vi.com\"]"}, {"Name": "FRONTEND_HOST", "Value": "https://dashboard.dev.hi-vi.com"}], "Essential": true, "HealthCheck": {"Command": ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/utils/health-check/ || exit 1"], "Interval": 30, "Retries": 3, "StartPeriod": 60, "Timeout": 5}, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "HiViBackendRepositoryD192C98A"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "HiViLogGroupB9322250"}, "awslogs-stream-prefix": "ecs", "awslogs-region": "us-east-1"}}, "Name": "HiViBackendContainer", "PortMappings": [{"ContainerPort": 8000, "Protocol": "tcp"}], "Secrets": [{"Name": "POSTGRES_PASSWORD", "ValueFrom": {"Fn::Join": ["", [{"Ref": "DatabasePassword49A8070F"}, ":password::"]]}}, {"Name": "SECRET_KEY", "ValueFrom": {"Ref": "JwtSecretB8834B39"}}, {"Name": "STRIPE_SECRET_KEY", "ValueFrom": {"Ref": "StripeSecret80A38A68"}}]}], "Cpu": "512", "ExecutionRoleArn": {"Fn::GetAtt": ["HiViTaskDefinitionExecutionRole815CDE10", "<PERSON><PERSON>"]}, "Family": "HiViDevelopmentStackHiViTaskDefinition878CB736", "Memory": "1024", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["HiViTaskDefinitionTaskRole684C2C51", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTaskDefinition/Resource"}}, "HiViTaskDefinitionExecutionRole815CDE10": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/Resource"}}, "HiViTaskDefinitionExecutionRoleDefaultPolicyBBB2E439": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["HiViLogGroupB9322250", "<PERSON><PERSON>"]}}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": [{"Ref": "DatabasePassword49A8070F"}, {"Ref": "JwtSecretB8834B39"}, {"Ref": "StripeSecret80A38A68"}]}], "Version": "2012-10-17"}, "PolicyName": "HiViTaskDefinitionExecutionRoleDefaultPolicyBBB2E439", "Roles": [{"Ref": "HiViTaskDefinitionExecutionRole815CDE10"}]}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViTaskDefinition/ExecutionRole/DefaultPolicy/Resource"}}, "HiViBackendService53A539D4": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Ref": "HiViCluster5D213706"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 1, "EnableECSManagedTags": false, "HealthCheckGracePeriodSeconds": 300, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "HiViBackendContainer", "ContainerPort": 8000, "TargetGroupArn": {"Ref": "HiViTargetGroup83A7AC09"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::GetAtt": ["EcsSecurityGroup44008BF1", "GroupId"]}], "Subnets": [{"Ref": "HiViVpcprivateSubnet1Subnet9F4E7AAD"}, {"Ref": "HiViVpcprivateSubnet2Subnet1821A4CF"}]}}, "ServiceName": "hivi-backend", "TaskDefinition": {"Ref": "HiViTaskDefinitionE4DE095E"}}, "DependsOn": ["HiViLoadBalancerHttpsListenerD1951CDE", "HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "HiViTaskDefinitionTaskRole684C2C51"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViBackendService/Service"}}, "HiViBackendServiceTaskCountTarget6C48E879": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 3, "MinCapacity": 1, "ResourceId": {"Fn::Join": ["", ["service/", {"Ref": "HiViCluster5D213706"}, "/", {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}]]}, "RoleARN": "arn:aws:iam::************:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "HiViTaskDefinitionTaskRole684C2C51"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/Resource"}}, "HiViBackendServiceTaskCountTargetCpuScaling8BBB29A9": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "HiViDevelopmentStackHiViBackendServiceTaskCountTargetCpuScaling3942098A", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "HiViBackendServiceTaskCountTarget6C48E879"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "HiViTaskDefinitionTaskRole684C2C51"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/CpuScaling/Resource"}}, "HiViBackendServiceTaskCountTargetMemoryScaling7DAC75E4": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "HiViDevelopmentStackHiViBackendServiceTaskCountTargetMemoryScalingDD5F2D99", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "HiViBackendServiceTaskCountTarget6C48E879"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["HiViTaskDefinitionTaskRoleDefaultPolicy5A6673CB", "HiViTaskDefinitionTaskRole684C2C51"], "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HiViBackendService/TaskCount/Target/MemoryScaling/Resource"}}, "HighCpuAlarm40723000": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Ref": "HiViCluster5D213706"}}, {"Name": "ServiceName", "Value": {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}}], "EvaluationPeriods": 2, "MetricName": "CPUUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 80, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HighCpuAlarm/Resource"}}, "HighMemoryAlarmB77DAB20": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "ClusterName", "Value": {"Ref": "HiViCluster5D213706"}}, {"Name": "ServiceName", "Value": {"Fn::GetAtt": ["HiViBackendService53A539D4", "Name"]}}], "EvaluationPeriods": 2, "MetricName": "MemoryUtilization", "Namespace": "AWS/ECS", "Period": 300, "Statistic": "Average", "Threshold": 85, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/HighMemoryAlarm/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/31UyY7bMAz9lt4VN03bD8gkswQYtEYczKGXgpYZjya2aGhJMDD876WseMm06ImPi6THR9qr5MtymSw/wcUuZHFaVCpP2syBPAkO/W5RrpL2pZFic9Qv6UakPq+UzHyu0YXYhPbkHR4gr3CKT7G1tSQVOEV6LA7gfpcG8wPcIzi8wLtIjToznC7eaYeG8VAQmVy9tWOurzVqJzKU3ij3/mjINz2H/wZ2ujRobScsSoPO1qChRMPd9/61PqBoDmBKdLP3xoKPmU6YwibtFhzkYHGnrQMtUcSWRjbbu78CQ2kn+OKk3WNDVjkyfdOTF9L8wKbylqUJuQE+MBUIktvTFo9Kq0HwjxHSDpRGM4tdz2ZozkrGIV5hJqEKQwyXbMiHDrEC65SsCIqck1oqXZ55VdZNw/vRz/mZc3d9LnK88ed1iqnra82AZ/ko76jSzGWhwx59/5q0T8QHi1+ke+Izb71HSaaIAgaUIdNnCk4dwwM4zn0zxXpJJ7cTFZUs+DOVI40Bd0JBzaOiuPe9TYm59zOLqBMwtQPekWVBWTDetVHZ0FSv+W0kmoPhD5IPZPHcdP9NgNuqyBcXcPKVJ1GBqUNND7pO7NGSN2GyvCtUT25Q5p+p1NBZFWEa1qLjv0LJb4X6n941nmXUVGDyZj+fV8vkG/9F3qxSC8MLompM9tH+AfiA0bhiBAAA"}, "Metadata": {"aws:cdk:path": "HiViDevelopmentStack/CDKMetadata/Default"}}}, "Outputs": {"LoadBalancerDNS": {"Description": "DNS name of the load balancer", "Value": {"Fn::GetAtt": ["HiViLoadBalancer0C0CF18D", "DNSName"]}}, "DatabaseEndpoint": {"Description": "RDS database endpoint", "Value": {"Fn::GetAtt": ["HiViDatabase96DE84D7", "Endpoint.Address"]}}, "ECRRepositoryURI": {"Description": "ECR repository URI", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["HiViBackendRepositoryD192C98A", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "HiViBackendRepositoryD192C98A"}]]}}, "ApiUrl": {"Description": "API base URL", "Value": "https://api.dev.hi-vi.com"}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}