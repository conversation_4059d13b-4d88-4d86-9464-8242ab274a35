{"name": "hivi-infrastructure", "version": "1.0.0", "description": "AWS CDK infrastructure for Hi-Vi Spiritual Coaching Platform", "bin": {"hivi-infrastructure": "bin/hivi-infrastructure.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "test:watch": "jest --watch", "cdk": "cdk", "deploy:dev": "cdk deploy HiViDevelopmentStack --context environment=development", "deploy:staging": "cdk deploy HiViStagingStack --context environment=staging", "deploy:prod": "cdk deploy HiViProductionStack --context environment=production", "destroy:dev": "cdk destroy HiViDevelopmentStack --context environment=development", "destroy:staging": "cdk destroy HiViStagingStack --context environment=staging", "destroy:prod": "cdk destroy HiViProductionStack --context environment=production", "diff:dev": "cdk diff HiViDevelopmentStack --context environment=development", "diff:staging": "cdk diff HiViStagingStack --context environment=staging", "diff:prod": "cdk diff HiViProductionStack --context environment=production", "synth": "cdk synth", "synth:dev": "cdk synth HiViDevelopmentStack --context environment=development", "synth:staging": "cdk synth HiViStagingStack --context environment=staging", "synth:prod": "cdk synth HiViProductionStack --context environment=production"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "20.6.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "aws-cdk": "2.100.0", "ts-node": "^10.9.1", "typescript": "~5.2.2"}, "dependencies": {"aws-cdk-lib": "2.100.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}, "keywords": ["aws", "cdk", "infrastructure", "hivi", "spiritual-coaching"], "author": "Hi-Vi Development Team", "license": "MIT"}