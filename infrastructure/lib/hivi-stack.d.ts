import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { Construct } from 'constructs';
export interface HiViStackProps extends cdk.StackProps {
    domainName: string;
    certificateArn?: string;
    environment: 'development' | 'staging' | 'production';
}
export declare class HiViStack extends cdk.Stack {
    readonly vpc: ec2.Vpc;
    readonly cluster: ecs.Cluster;
    readonly database: rds.DatabaseInstance;
    readonly loadBalancer: elbv2.ApplicationLoadBalancer;
    readonly ecrRepository: ecr.Repository;
    readonly backendService: ecs.FargateService;
    constructor(scope: Construct, id: string, props: HiViStackProps);
}
