import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as applicationautoscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import { Construct } from 'constructs';

export interface HiViStackProps extends cdk.StackProps {
  domainName: string;
  certificateArn?: string;
  environment: 'development' | 'staging' | 'production';
}

export class HiViStack extends cdk.Stack {
  public readonly vpc: ec2.Vpc;
  public readonly cluster: ecs.Cluster;
  public readonly database: rds.DatabaseInstance;
  public readonly loadBalancer: elbv2.ApplicationLoadBalancer;
  public readonly ecrRepository: ecr.Repository;
  public readonly backendService: ecs.FargateService;

  constructor(scope: Construct, id: string, props: HiViStackProps) {
    super(scope, id, props);

    const { domainName, environment } = props;

    // VPC Configuration
    this.vpc = new ec2.Vpc(this, 'HiViVpc', {
      maxAzs: 2,
      natGateways: environment === 'production' ? 2 : 1,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 28,
          name: 'isolated',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
    });

    // Security Groups
    const albSecurityGroup = new ec2.SecurityGroup(this, 'AlbSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for Application Load Balancer',
      allowAllOutbound: true,
    });

    albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(80),
      'Allow HTTP traffic'
    );

    albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      'Allow HTTPS traffic'
    );

    const ecsSecurityGroup = new ec2.SecurityGroup(this, 'EcsSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for ECS tasks',
      allowAllOutbound: true,
    });

    ecsSecurityGroup.addIngressRule(
      albSecurityGroup,
      ec2.Port.tcp(8000),
      'Allow traffic from ALB'
    );

    const rdsSecurityGroup = new ec2.SecurityGroup(this, 'RdsSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for RDS database',
      allowAllOutbound: false,
    });

    rdsSecurityGroup.addIngressRule(
      ecsSecurityGroup,
      ec2.Port.tcp(5432),
      'Allow PostgreSQL traffic from ECS'
    );

    // Secrets Manager
    const dbPassword = new secretsmanager.Secret(this, 'DatabasePassword', {
      description: 'PostgreSQL password for Hi-Vi backend',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ username: 'hivi_admin' }),
        generateStringKey: 'password',
        excludeCharacters: '"@/\\',
        passwordLength: 32,
      },
    });

    const jwtSecret = new secretsmanager.Secret(this, 'JwtSecret', {
      description: 'JWT secret key for Hi-Vi backend',
      generateSecretString: {
        passwordLength: 64,
        excludeCharacters: '"@/\\',
      },
    });

    const stripeSecret = new secretsmanager.Secret(this, 'StripeSecret', {
      description: 'Stripe secret key for Hi-Vi backend',
      secretStringValue: cdk.SecretValue.unsafePlainText('sk_test_placeholder'),
    });

    // RDS Database
    this.database = new rds.DatabaseInstance(this, 'HiViDatabase', {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_14_5,
      }),
      instanceType: environment === 'production'
        ? ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.SMALL)
        : ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MICRO),
      credentials: rds.Credentials.fromSecret(dbPassword),
      vpc: this.vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
      securityGroups: [rdsSecurityGroup],
      databaseName: 'hivi_production',
      allocatedStorage: environment === 'production' ? 100 : 20,
      storageEncrypted: true,
      backupRetention: cdk.Duration.days(environment === 'production' ? 30 : 7),
      deletionProtection: environment === 'production',
      removalPolicy: environment === 'production'
        ? cdk.RemovalPolicy.RETAIN
        : cdk.RemovalPolicy.DESTROY,
    });

    // ECR Repository
    this.ecrRepository = new ecr.Repository(this, 'HiViBackendRepository', {
      repositoryName: `hivi-backend-${environment}`,
      imageScanOnPush: true,
      lifecycleRules: [
        {
          maxImageCount: 10,
          tagStatus: ecr.TagStatus.UNTAGGED,
        },
        {
          maxImageAge: cdk.Duration.days(30),
          tagStatus: ecr.TagStatus.ANY,
        },
      ],
    });

    // ECS Cluster
    this.cluster = new ecs.Cluster(this, 'HiViCluster', {
      vpc: this.vpc,
      clusterName: 'hivi-cluster',
      containerInsights: environment === 'production',
    });

    // Application Load Balancer
    this.loadBalancer = new elbv2.ApplicationLoadBalancer(this, 'HiViLoadBalancer', {
      vpc: this.vpc,
      internetFacing: true,
      securityGroup: albSecurityGroup,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PUBLIC,
      },
    });

    // SSL Certificate
    let certificate: acm.ICertificate;
    if (props.certificateArn) {
      certificate = acm.Certificate.fromCertificateArn(
        this,
        'Certificate',
        props.certificateArn
      );
    } else {
      // Create hosted zone if it doesn't exist
      const hostedZone = new route53.HostedZone(this, 'HiViHostedZone', {
        zoneName: domainName,
      });

      certificate = new acm.Certificate(this, 'HiViCertificate', {
        domainName: `api.${domainName}`,
        validation: acm.CertificateValidation.fromDns(hostedZone),
      });

      // Create A record for API subdomain
      new route53.ARecord(this, 'ApiAliasRecord', {
        zone: hostedZone,
        recordName: 'api',
        target: route53.RecordTarget.fromAlias(
          new route53targets.LoadBalancerTarget(this.loadBalancer)
        ),
      });
    }

    // Target Group
    const targetGroup = new elbv2.ApplicationTargetGroup(this, 'HiViTargetGroup', {
      vpc: this.vpc,
      port: 8000,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP,
      healthCheck: {
        enabled: true,
        path: '/api/v1/utils/health-check/',
        protocol: elbv2.Protocol.HTTP,
        healthyHttpCodes: '200',
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 3,
      },
    });

    // HTTPS Listener
    this.loadBalancer.addListener('HttpsListener', {
      port: 443,
      protocol: elbv2.ApplicationProtocol.HTTPS,
      certificates: [certificate],
      defaultTargetGroups: [targetGroup],
    });

    // HTTP to HTTPS Redirect
    this.loadBalancer.addListener('HttpListener', {
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      defaultAction: elbv2.ListenerAction.redirect({
        protocol: 'HTTPS',
        port: '443',
        permanent: true,
      }),
    });

    // CloudWatch Log Group
    const logGroup = new logs.LogGroup(this, 'HiViLogGroup', {
      logGroupName: '/ecs/hivi-backend',
      retention: environment === 'production'
        ? logs.RetentionDays.ONE_MONTH
        : logs.RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // ECS Task Definition
    const taskDefinition = new ecs.FargateTaskDefinition(this, 'HiViTaskDefinition', {
      memoryLimitMiB: environment === 'production' ? 2048 : 1024,
      cpu: environment === 'production' ? 1024 : 512,
    });

    // Grant permissions to access secrets
    dbPassword.grantRead(taskDefinition.taskRole);
    jwtSecret.grantRead(taskDefinition.taskRole);
    stripeSecret.grantRead(taskDefinition.taskRole);

    // Container Definition
    const container = taskDefinition.addContainer('HiViBackendContainer', {
      image: ecs.ContainerImage.fromEcrRepository(this.ecrRepository, 'latest'),
      environment: {
        ENVIRONMENT: environment,
        PROJECT_NAME: 'Hi-Vi Spiritual Coaching Platform',
        API_V1_STR: '/api/v1',
        POSTGRES_SERVER: this.database.instanceEndpoint.hostname,
        POSTGRES_PORT: '5432',
        POSTGRES_USER: 'hivi_admin',
        POSTGRES_DB: 'hivi_production',
        BACKEND_CORS_ORIGINS: JSON.stringify([
          `https://dashboard.${domainName}`,
          `https://${domainName}`,
        ]),
        FRONTEND_HOST: `https://dashboard.${domainName}`,
      },
      secrets: {
        POSTGRES_PASSWORD: ecs.Secret.fromSecretsManager(dbPassword, 'password'),
        SECRET_KEY: ecs.Secret.fromSecretsManager(jwtSecret),
        STRIPE_SECRET_KEY: ecs.Secret.fromSecretsManager(stripeSecret),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ecs',
        logGroup: logGroup,
      }),
      healthCheck: {
        command: [
          'CMD-SHELL',
          'curl -f http://localhost:8000/api/v1/utils/health-check/ || exit 1',
        ],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        retries: 3,
        startPeriod: cdk.Duration.seconds(60),
      },
    });

    container.addPortMappings({
      containerPort: 8000,
      protocol: ecs.Protocol.TCP,
    });

    // ECS Service
    this.backendService = new ecs.FargateService(this, 'HiViBackendService', {
      cluster: this.cluster,
      taskDefinition: taskDefinition,
      serviceName: 'hivi-backend',
      desiredCount: environment === 'production' ? 2 : 1,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroups: [ecsSecurityGroup],
      assignPublicIp: false,
      healthCheckGracePeriod: cdk.Duration.seconds(300),
    });

    // Attach service to target group
    this.backendService.attachToApplicationTargetGroup(targetGroup);

    // Auto Scaling
    const scalableTarget = this.backendService.autoScaleTaskCount({
      minCapacity: environment === 'production' ? 2 : 1,
      maxCapacity: environment === 'production' ? 10 : 3,
    });

    scalableTarget.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });

    scalableTarget.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 80,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });

    // CloudWatch Alarms
    new cloudwatch.Alarm(this, 'HighCpuAlarm', {
      metric: this.backendService.metricCpuUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    new cloudwatch.Alarm(this, 'HighMemoryAlarm', {
      metric: this.backendService.metricMemoryUtilization(),
      threshold: 85,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    // Outputs
    new cdk.CfnOutput(this, 'LoadBalancerDNS', {
      value: this.loadBalancer.loadBalancerDnsName,
      description: 'DNS name of the load balancer',
    });

    new cdk.CfnOutput(this, 'DatabaseEndpoint', {
      value: this.database.instanceEndpoint.hostname,
      description: 'RDS database endpoint',
    });

    new cdk.CfnOutput(this, 'ECRRepositoryURI', {
      value: this.ecrRepository.repositoryUri,
      description: 'ECR repository URI',
    });

    new cdk.CfnOutput(this, 'ApiUrl', {
      value: `https://api.${domainName}`,
      description: 'API base URL',
    });
  }
}
