"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const cdk = __importStar(require("aws-cdk-lib"));
const assertions_1 = require("aws-cdk-lib/assertions");
const hivi_stack_1 = require("../lib/hivi-stack");
describe('HiViStack', () => {
    let app;
    let stack;
    let template;
    beforeEach(() => {
        app = new cdk.App();
        stack = new hivi_stack_1.HiViStack(app, 'TestHiViStack', {
            domainName: 'test.hi-vi.com',
            environment: 'development',
        });
        template = assertions_1.Template.fromStack(stack);
    });
    test('VPC is created with correct configuration', () => {
        template.hasResourceProperties('AWS::EC2::VPC', {
            CidrBlock: '10.0.0.0/16',
            EnableDnsHostnames: true,
            EnableDnsSupport: true,
        });
    });
    test('RDS instance is created', () => {
        template.hasResourceProperties('AWS::RDS::DBInstance', {
            Engine: 'postgres',
            DBInstanceClass: 'db.t3.micro',
            AllocatedStorage: '20',
            StorageEncrypted: true,
        });
    });
    test('ECS cluster is created', () => {
        template.hasResourceProperties('AWS::ECS::Cluster', {
            ClusterName: 'hivi-cluster',
        });
    });
    test('ECR repository is created', () => {
        template.hasResourceProperties('AWS::ECR::Repository', {
            RepositoryName: 'hivi-backend',
            ImageScanningConfiguration: {
                ScanOnPush: true,
            },
        });
    });
    test('Application Load Balancer is created', () => {
        template.hasResourceProperties('AWS::ElasticLoadBalancingV2::LoadBalancer', {
            Type: 'application',
            Scheme: 'internet-facing',
        });
    });
    test('ECS service is created with correct configuration', () => {
        template.hasResourceProperties('AWS::ECS::Service', {
            ServiceName: 'hivi-backend',
            LaunchType: 'FARGATE',
            DesiredCount: 1, // Development environment
        });
    });
    test('Security groups are created with correct rules', () => {
        // ALB Security Group
        template.hasResourceProperties('AWS::EC2::SecurityGroup', {
            GroupDescription: 'Security group for Application Load Balancer',
            SecurityGroupIngress: [
                {
                    IpProtocol: 'tcp',
                    FromPort: 80,
                    ToPort: 80,
                    CidrIp: '0.0.0.0/0',
                },
                {
                    IpProtocol: 'tcp',
                    FromPort: 443,
                    ToPort: 443,
                    CidrIp: '0.0.0.0/0',
                },
            ],
        });
        // ECS Security Group
        template.hasResourceProperties('AWS::EC2::SecurityGroup', {
            GroupDescription: 'Security group for ECS tasks',
        });
        // RDS Security Group
        template.hasResourceProperties('AWS::EC2::SecurityGroup', {
            GroupDescription: 'Security group for RDS database',
        });
    });
    test('Secrets are created', () => {
        template.hasResourceProperties('AWS::SecretsManager::Secret', {
            Description: 'PostgreSQL password for Hi-Vi backend',
        });
        template.hasResourceProperties('AWS::SecretsManager::Secret', {
            Description: 'JWT secret key for Hi-Vi backend',
        });
        template.hasResourceProperties('AWS::SecretsManager::Secret', {
            Description: 'Stripe secret key for Hi-Vi backend',
        });
    });
    test('CloudWatch log group is created', () => {
        template.hasResourceProperties('AWS::Logs::LogGroup', {
            LogGroupName: '/ecs/hivi-backend',
            RetentionInDays: 7, // Development environment
        });
    });
    test('Auto scaling is configured', () => {
        template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalableTarget', {
            ServiceNamespace: 'ecs',
            ScalableDimension: 'ecs:service:DesiredCount',
            MinCapacity: 1,
            MaxCapacity: 3,
        });
        template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalingPolicy', {
            PolicyType: 'TargetTrackingScaling',
        });
    });
    test('CloudWatch alarms are created', () => {
        template.hasResourceProperties('AWS::CloudWatch::Alarm', {
            MetricName: 'CPUUtilization',
            Namespace: 'AWS/ECS',
            Threshold: 80,
        });
        template.hasResourceProperties('AWS::CloudWatch::Alarm', {
            MetricName: 'MemoryUtilization',
            Namespace: 'AWS/ECS',
            Threshold: 85,
        });
    });
    test('Target group health check is configured correctly', () => {
        template.hasResourceProperties('AWS::ElasticLoadBalancingV2::TargetGroup', {
            HealthCheckPath: '/api/v1/utils/health-check/',
            HealthCheckProtocol: 'HTTP',
            HealthyThresholdCount: 2,
            UnhealthyThresholdCount: 3,
            HealthCheckIntervalSeconds: 30,
            HealthCheckTimeoutSeconds: 5,
        });
    });
    test('HTTPS listener is configured with redirect from HTTP', () => {
        template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
            Port: 443,
            Protocol: 'HTTPS',
        });
        template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
            Port: 80,
            Protocol: 'HTTP',
            DefaultActions: [
                {
                    Type: 'redirect',
                    RedirectConfig: {
                        Protocol: 'HTTPS',
                        Port: '443',
                        StatusCode: 'HTTP_301',
                    },
                },
            ],
        });
    });
});
describe('HiViStack Production Configuration', () => {
    let app;
    let stack;
    let template;
    beforeEach(() => {
        app = new cdk.App();
        stack = new hivi_stack_1.HiViStack(app, 'TestHiViProductionStack', {
            domainName: 'hi-vi.com',
            environment: 'production',
        });
        template = assertions_1.Template.fromStack(stack);
    });
    test('Production environment has correct scaling configuration', () => {
        template.hasResourceProperties('AWS::ECS::Service', {
            DesiredCount: 2, // Production environment
        });
        template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalableTarget', {
            MinCapacity: 2,
            MaxCapacity: 10,
        });
    });
    test('Production RDS has correct configuration', () => {
        template.hasResourceProperties('AWS::RDS::DBInstance', {
            DBInstanceClass: 'db.t3.small',
            AllocatedStorage: '100',
            BackupRetentionPeriod: 30,
            DeletionProtection: true,
        });
    });
    test('Production has longer log retention', () => {
        template.hasResourceProperties('AWS::Logs::LogGroup', {
            RetentionInDays: 30,
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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