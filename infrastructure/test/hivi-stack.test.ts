import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { HiViStack } from '../lib/hivi-stack';

describe('HiViStack', () => {
  let app: cdk.App;
  let stack: HiViStack;
  let template: Template;

  beforeEach(() => {
    app = new cdk.App();
    stack = new HiViStack(app, 'TestHiViStack', {
      domainName: 'test.hi-vi.com',
      environment: 'development',
    });
    template = Template.fromStack(stack);
  });

  test('VPC is created with correct configuration', () => {
    template.hasResourceProperties('AWS::EC2::VPC', {
      CidrBlock: '10.0.0.0/16',
      EnableDnsHostnames: true,
      EnableDnsSupport: true,
    });
  });

  test('RDS instance is created', () => {
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      Engine: 'postgres',
      DBInstanceClass: 'db.t3.micro',
      AllocatedStorage: '20',
      StorageEncrypted: true,
    });
  });

  test('ECS cluster is created', () => {
    template.hasResourceProperties('AWS::ECS::Cluster', {
      ClusterName: 'hivi-cluster',
    });
  });

  test('ECR repository is created', () => {
    template.hasResourceProperties('AWS::ECR::Repository', {
      RepositoryName: 'hivi-backend',
      ImageScanningConfiguration: {
        ScanOnPush: true,
      },
    });
  });

  test('Application Load Balancer is created', () => {
    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::LoadBalancer', {
      Type: 'application',
      Scheme: 'internet-facing',
    });
  });

  test('ECS service is created with correct configuration', () => {
    template.hasResourceProperties('AWS::ECS::Service', {
      ServiceName: 'hivi-backend',
      LaunchType: 'FARGATE',
      DesiredCount: 1, // Development environment
    });
  });

  test('Security groups are created with correct rules', () => {
    // ALB Security Group
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: 'Security group for Application Load Balancer',
      SecurityGroupIngress: [
        {
          IpProtocol: 'tcp',
          FromPort: 80,
          ToPort: 80,
          CidrIp: '0.0.0.0/0',
        },
        {
          IpProtocol: 'tcp',
          FromPort: 443,
          ToPort: 443,
          CidrIp: '0.0.0.0/0',
        },
      ],
    });

    // ECS Security Group
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: 'Security group for ECS tasks',
    });

    // RDS Security Group
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: 'Security group for RDS database',
    });
  });

  test('Secrets are created', () => {
    template.hasResourceProperties('AWS::SecretsManager::Secret', {
      Description: 'PostgreSQL password for Hi-Vi backend',
    });

    template.hasResourceProperties('AWS::SecretsManager::Secret', {
      Description: 'JWT secret key for Hi-Vi backend',
    });

    template.hasResourceProperties('AWS::SecretsManager::Secret', {
      Description: 'Stripe secret key for Hi-Vi backend',
    });
  });

  test('CloudWatch log group is created', () => {
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      LogGroupName: '/ecs/hivi-backend',
      RetentionInDays: 7, // Development environment
    });
  });

  test('Auto scaling is configured', () => {
    template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalableTarget', {
      ServiceNamespace: 'ecs',
      ScalableDimension: 'ecs:service:DesiredCount',
      MinCapacity: 1,
      MaxCapacity: 3,
    });

    template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalingPolicy', {
      PolicyType: 'TargetTrackingScaling',
    });
  });

  test('CloudWatch alarms are created', () => {
    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      MetricName: 'CPUUtilization',
      Namespace: 'AWS/ECS',
      Threshold: 80,
    });

    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      MetricName: 'MemoryUtilization',
      Namespace: 'AWS/ECS',
      Threshold: 85,
    });
  });

  test('Target group health check is configured correctly', () => {
    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::TargetGroup', {
      HealthCheckPath: '/api/v1/utils/health-check/',
      HealthCheckProtocol: 'HTTP',
      HealthyThresholdCount: 2,
      UnhealthyThresholdCount: 3,
      HealthCheckIntervalSeconds: 30,
      HealthCheckTimeoutSeconds: 5,
    });
  });

  test('HTTPS listener is configured with redirect from HTTP', () => {
    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
      Port: 443,
      Protocol: 'HTTPS',
    });

    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
      Port: 80,
      Protocol: 'HTTP',
      DefaultActions: [
        {
          Type: 'redirect',
          RedirectConfig: {
            Protocol: 'HTTPS',
            Port: '443',
            StatusCode: 'HTTP_301',
          },
        },
      ],
    });
  });
});

describe('HiViStack Production Configuration', () => {
  let app: cdk.App;
  let stack: HiViStack;
  let template: Template;

  beforeEach(() => {
    app = new cdk.App();
    stack = new HiViStack(app, 'TestHiViProductionStack', {
      domainName: 'hi-vi.com',
      environment: 'production',
    });
    template = Template.fromStack(stack);
  });

  test('Production environment has correct scaling configuration', () => {
    template.hasResourceProperties('AWS::ECS::Service', {
      DesiredCount: 2, // Production environment
    });

    template.hasResourceProperties('AWS::ApplicationAutoScaling::ScalableTarget', {
      MinCapacity: 2,
      MaxCapacity: 10,
    });
  });

  test('Production RDS has correct configuration', () => {
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      DBInstanceClass: 'db.t3.small',
      AllocatedStorage: '100',
      BackupRetentionPeriod: 30,
      DeletionProtection: true,
    });
  });

  test('Production has longer log retention', () => {
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 30,
    });
  });
});
