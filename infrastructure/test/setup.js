"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Jest setup file for CDK tests
require("source-map-support/register");
// Set default timeout for tests
jest.setTimeout(30000);
// Mock AWS SDK calls during testing
jest.mock('aws-sdk', () => ({
    config: {
        update: jest.fn(),
    },
}));
// Set environment variables for testing
process.env.CDK_DEFAULT_ACCOUNT = '************';
process.env.CDK_DEFAULT_REGION = 'us-east-1';
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************